import type { IPickListValue, IPickList } from '@/services/api/crm/interfaces'
import type { IBillingInfo, IContact, IGdprRegistry, IUser } from '@/services/users/interfaces'
import type { IBillingData } from '@/services/users/interfaces/billing-data.interface'
import store from '@/store'
import {
  NEXT_STEP,
  PREV_STEP,
  SET_OLD_USER,
  REMOVE_OLD_USER,
  UPDATE_NEW_USER,
  SET_NEW_USER,
  REMOVE_NEW_USER,
  SET_COUNTRIES,
  SET_STREET_TYPES,
  SET_PICKLISTS,
  SET_BILLING_DATA,
  SET_BILLING_ADDRESS,
  SAVE_OPPORTUNITY_ID,
  SET_SIM_ADDRESS,
  REMOVE_SIM_ADDRESS,
  SET_COVERAGE,
  SET_BANK_ACCOUNT,
  SET_ACCOUNT_HOLDER,
  SET_STEP,
  SET_STEPS,
  STEP_COMPLETED,
  SET_COMPANY,
  SET_CHANNEL,
  SET_ACCESS_TOKEN,
  SET_CATALOG,
  SET_LANG,
  SET_RATE_CODE,
  SET_GDPR,
  SET_ADV,
  SET_RATE,
  SET_USERNAME,
  SET_TRADEMARK,
  SET_PRODUCT_FAMILY,
  SET_ACCOUNT_ID,
  SET_GESCAL,
  SET_USER_ID,
  SHOPPING,
  SET_FREEZE_CART,
  UPDATE_FREEZE_CART,
  GET_CHANNEL,
  RECOVER_SELLING_DATA,
  SET_USER_TYPE,
  SET_CHECKED_SIM_ADDRESS,
  CLEAN_SELLING_DATA,
  SET_USER_INFO,
  SET_SELECTED_DISCOUNT,
  SET_SHOW_NO_ACCOUNT_MESSAGE
  // SET_CHECKED_AUTHORIZE_PERSON,
  // SET_AUTHORIZED_PERSON
} from './constants/store.constants'
import type { IState } from '@/store/modules/shopping/interfaces/state.interface'
import type { ActionContext } from 'vuex'
import type { IUserForm } from '@/components/personal-data/interfaces/user-form.interface'
import { PICKLIST_COUNTRY, PICKLIST_STREET_TYPE } from '@/services/api/constants/api.constants'
import apiCrmService from '@/services/api/crm/apiCrmService'
import type { IAddressForm } from '@/components/billing-data/interfaces/address-form.interface'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import type { IStep } from '@/interfaces/step.interface'
import type { ICatalog } from '@/interfaces/catalog.interface'
import type { IAdvertisingConfiguration } from '@/services/users/interfaces/advertising-configuration.interface'
import type { ILeadSell } from '@/services/api/shoppingcart/interfaces/lead-sell.interface'
import apiShoppingcartService from '@/services/api/shoppingcart/apiShoppincartService'
import type { IApiRes } from '@/services/api/interfaces/api-res.interface'
import type { IRateDataParams } from '@/services/api/shoppingcart/interfaces/rate-data-params.interface'
import type { IRate } from '@/services/api/shoppingcart/interfaces/rate.interface'
import type { IfreezeCart } from '@/interfaces/freezeCart.interface'
import { CHANNEL_CALL_CENTER } from '@/services/constants/services.constants'
import type { IUserInfo } from '@/services/users/interfaces/user-info.interface'

export const nextStep = (context: ActionContext<IState, IState>): void => {
  context.commit(NEXT_STEP)
}

export const stepCompleted = (context: ActionContext<IState, IState>, step: number): void => {
  context.commit(STEP_COMPLETED, step)
}

export const prevStep = (context: ActionContext<IState, IState>): void => {
  context.commit(PREV_STEP)
}

export const setStep = (context: ActionContext<IState, IState>, step: number): void => {
  context.commit(SET_STEP, step)
}

export const setSteps = (context: ActionContext<IState, IState>, steps: IStep[]): void => {
  context.commit(SET_STEPS, steps)
}

export const setCompany = (context: ActionContext<IState, IState>, company: string): void => {
  context.commit(SET_COMPANY, company)
}

export const setShowNoAccountMessage = (
  context: ActionContext<IState, IState>,
  showNoAccountMessage: boolean
): void => {
  context.commit(SET_SHOW_NO_ACCOUNT_MESSAGE, showNoAccountMessage)
}

export const setChannel = (context: ActionContext<IState, IState>, channel: string): void => {
  context.commit(SET_CHANNEL, channel)
}

export const setTrademark = (context: ActionContext<IState, IState>, trademark: string): void => {
  context.commit(SET_TRADEMARK, trademark)
}

export const setSelectedDiscount = (
  context: ActionContext<IState, IState>,
  selectedDiscount: IPickListValue
): void => {
  context.commit(SET_SELECTED_DISCOUNT, selectedDiscount)
}

export const setUsername = (context: ActionContext<IState, IState>, username: string): void => {
  context.commit(SET_USERNAME, username)
}

export const setProductFamily = (
  context: ActionContext<IState, IState>,
  productFamily: string | undefined
): void => {
  context.commit(SET_PRODUCT_FAMILY, productFamily)
}

export const setCheckedSimAddress = (
  context: ActionContext<IState, IState>,
  checkedSimAddress: boolean
): void => {
  context.commit(SET_CHECKED_SIM_ADDRESS, checkedSimAddress)
}

// export const setCheckedAuthorizePerson = (
//   context: ActionContext<IState, IState>,
//   checkedAuthorizePerson: boolean
// ): void => {
//   context.commit(SET_CHECKED_AUTHORIZE_PERSON, checkedAuthorizePerson)
// }

// export const setAuthorizedPerson = (
//   context: ActionContext<IState, IState>,
//   authorizedPerson: { name: string; phone: string }
// ): void => {
//   context.commit(SET_AUTHORIZED_PERSON, authorizedPerson)
// }

export const setRateCode = (
  context: ActionContext<IState, IState>,
  rateCode: string | undefined
): void => {
  context.commit(SET_RATE_CODE, rateCode)
}

export const getUserInfo = async (
  context: ActionContext<IState, IState>,
  username: string
): Promise<IUserInfo> => {
  const userInfo: IUserInfo = await apiShoppingcartService.getUserInfo(username)
  context.dispatch(SET_USER_INFO, userInfo)
  return userInfo
}

export const setUserInfo = (context: ActionContext<IState, IState>, userInfo: IUserInfo): void => {
  context.commit(SET_USER_INFO, userInfo)
  if (userInfo.username) {
    context.commit(SET_USERNAME, userInfo.username)
    context.commit(SET_CHANNEL, userInfo.channel)
    context.commit(SET_USER_TYPE, userInfo.isOnline)
    userInfo.company?.length && context.commit(SET_COMPANY, userInfo.company[0])
  }
}

export const getRateByCode = async (
  context: ActionContext<IState, IState>,
  rateData: IRateDataParams
): Promise<void> => {
  const rate: IRate = await apiShoppingcartService.getRateByCode(rateData)
  context.commit(SET_RATE, rate)
}

export const getRateGroups = async (
  context: ActionContext<IState, IState>,
  rateData: IRateDataParams
): Promise<void> => {
  const ratesGroups: IRate[] = await apiShoppingcartService.getRateGroups(rateData)
  const rateGroup: IRate =
    ratesGroups?.find((element: any) => element.startPosition) || ratesGroups[0]
  if (rateGroup) {
    rateData.rateGroupId = rateGroup.id
    let rate: IRate | IRate[] = await apiShoppingcartService.getRateById(rateData)
    rate = Array.isArray(rate) ? rate[0] : rate
    context.commit(SET_RATE, rate)
  }
}

export const setLang = (context: ActionContext<IState, IState>, lang: string): void => {
  context.commit(SET_LANG, lang)
}
export const setUserType = (context: ActionContext<IState, IState>, userType: string): void => {
  context.commit(SET_USER_TYPE, userType)
}

export const setNewUser = (context: ActionContext<IState, IState>, user: IUser): void => {
  context.commit(SET_NEW_USER, user)
}

export const updateNewUser = (
  context: ActionContext<IState, IState>,
  userForm: IUserForm
): void => {
  const oldUser = context.state.oldUser
  // Extraer valores de userForm
  const getUserFormFieldValue = (fieldName: string): any => {
    const field = userForm.find((f: IUserForm) => f.name === fieldName)
    return field ? field.value : null
  }

  if (oldUser) {
    const newUser: any = { ...oldUser }
    newUser.provisionContacts = newUser.provisionContacts.map((contact: IContact) => {
      if (contact.isDefault) {
        contact.phone = getUserFormFieldValue('phone') || contact.phone
        contact.email = getUserFormFieldValue('email') || contact.email
      }
      return contact
    })

    const newNationality = getUserFormFieldValue('nationality')
    if (newNationality !== undefined && newNationality !== null) {
      newUser.personalData.nationality = newNationality
    }

    const companyManagerFirstName = getUserFormFieldValue('companyManagerFirstName')
    if (companyManagerFirstName !== undefined && companyManagerFirstName !== null) {
      newUser.companyStructure.companyManagerFirstName = companyManagerFirstName
    }

    const companyManagerLastName = getUserFormFieldValue('companyManagerLastName')
    if (companyManagerLastName !== undefined && companyManagerLastName !== null) {
      newUser.companyStructure.companyManagerLastName = companyManagerLastName
    }

    const companyManagerAdministratorDocumentNumber = getUserFormFieldValue(
      'administratorDocumentNumber'
    )
    if (companyManagerAdministratorDocumentNumber) {
      newUser.companyStructure.administratorDocumentNumber =
        companyManagerAdministratorDocumentNumber
      const isNIE =
        companyManagerAdministratorDocumentNumber.startsWith('X') ||
        companyManagerAdministratorDocumentNumber.startsWith('Y') ||
        companyManagerAdministratorDocumentNumber.startsWith('Z')
      newUser.companyStructure.administratorDocumentType = isNIE ? 'NIE' : 'NIF'
    }

    context.commit(UPDATE_NEW_USER, newUser)
    const findBillingInfo = newUser.billingInfos?.find((info: IBillingInfo) => info.isDefault)
    if (findBillingInfo) {
      const billingData: IBillingData = {
        iban: findBillingInfo?.iban as string,
        cccOwner: findBillingInfo?.cccOwner as string
      } as IBillingData
      context.commit(SET_BILLING_DATA, { billingData: billingData, isChanged: false })
    }
    return
  }

  if (!oldUser) {
    const personalData: any = {}
    const provisionContacts: any = []
    const companyStructure: any = {}

    const firstName = getUserFormFieldValue('firstName')
    const lastName = getUserFormFieldValue('lastName')
    const documentNumber = getUserFormFieldValue('documentNumber')
    const nationality = getUserFormFieldValue('nationality')
    const personBirthdate = getUserFormFieldValue('birthdate')
    const customerType = getUserFormFieldValue('customerType')

    const completeName = getUserFormFieldValue('completeName')
    const foundationDate = getUserFormFieldValue('foundationDate')
    const companyManagerFirstName = getUserFormFieldValue('companyManagerFirstName')
    const companyManagerLastName = getUserFormFieldValue('companyManagerLastName')
    const administratorDocumentNumber = getUserFormFieldValue('administratorDocumentNumber')
    const adminDocumentType = getUserFormFieldValue('administratorDocumentType')

    if (
      completeName ||
      foundationDate ||
      companyManagerFirstName ||
      companyManagerLastName ||
      administratorDocumentNumber
    ) {
      if (completeName) personalData.completeName = completeName
      if (documentNumber) personalData.documentNumber = documentNumber
      if (foundationDate) personalData.foundationDate = foundationDate
      personalData.customerType = 'CustomerCompany'
      personalData.nationality = 'ES'
      personalData.documentType = 'CIF'
      companyStructure.companyManagerFirstName = companyManagerFirstName
      companyStructure.companyManagerLastName = companyManagerLastName
      companyStructure.administratorDocumentNumber = administratorDocumentNumber
      companyStructure.administratorDocumentType = adminDocumentType

      const phone = getUserFormFieldValue('phone')
      const email = getUserFormFieldValue('email')

      if (phone || email) {
        provisionContacts.push({
          isDefault: true,
          phone: phone || '',
          email: email || '',
          name: `${companyManagerFirstName || ''} ${companyManagerLastName || ''}`
        })
      }
    } else {
      if (firstName) personalData.firstName = firstName
      if (lastName) personalData.lastName = lastName
      if (documentNumber) personalData.documentNumber = documentNumber
      if (personBirthdate) personalData.personBirthdate = personBirthdate
      if (customerType) personalData.customerType = customerType
      if (nationality) personalData.nationality = nationality

      const phone = getUserFormFieldValue('phone')
      const email = getUserFormFieldValue('email')

      if (phone || email) {
        provisionContacts.push({
          isDefault: true,
          phone: phone || '',
          email: email || '',
          name: `${firstName || ''} ${lastName || ''}`
        })
      }
    }

    const newUser: IUser = {
      advertisingConfigurations: [],
      gdprRegistries: [],
      shippingAddresses: [],
      billingAddresses: [],
      personalData: Object.keys(personalData).length ? personalData : undefined,
      provisionContacts: provisionContacts.length ? provisionContacts : undefined,
      companyStructure: Object.keys(companyStructure).length ? companyStructure : undefined
    }

    context.commit(UPDATE_NEW_USER, newUser)
  }
}

export const setGdpr = (context: ActionContext<IState, IState>, gdpr: IGdprRegistry[]): void => {
  context.commit(SET_GDPR, gdpr)
}

export const setAdv = (
  context: ActionContext<IState, IState>,
  adv: IAdvertisingConfiguration[]
): void => {
  context.commit(SET_ADV, adv)
}

export const removeNewUser = (context: ActionContext<IState, IState>): void => {
  context.commit(REMOVE_NEW_USER)
}

export const setOldUser = (context: ActionContext<IState, IState>, user: IUser): void => {
  context.commit(SET_OLD_USER, user)
}

export const removeOldUser = (context: ActionContext<IState, IState>): void => {
  context.commit(REMOVE_OLD_USER)
}

export const saveOpportunityId = (
  context: ActionContext<IState, IState>,
  opportunityId: IUser
): void => {
  context.commit(SAVE_OPPORTUNITY_ID, opportunityId)
}

export const getPickLists = async (
  context: ActionContext<IState, IState>,
  lang: string
): Promise<void> => {
  const pickLists: IPickList[] = await apiCrmService.getPickLists(lang)
  if (pickLists?.length) {
    context.commit(SET_PICKLISTS, pickLists)
    const countries: IPickList | undefined = pickLists.find(
      (el: IPickList) => el?.name === PICKLIST_COUNTRY
    )
    const countryValues: IPickListValue[] = countries?.values?.length ? countries.values : []
    context.commit(SET_COUNTRIES, countryValues)
    const streetTypes: IPickList | undefined = pickLists.find(
      (el: IPickList) => el?.name === PICKLIST_STREET_TYPE
    )
    let streetTypesValues: IPickListValue[] = streetTypes?.values?.length ? streetTypes.values : []
    streetTypesValues = streetTypesValues.map((streetTypeValue: IPickListValue) => {
      streetTypeValue.translation = `streetType.${streetTypeValue.value?.toLowerCase()}`
      return streetTypeValue
    })
    context.commit(SET_STREET_TYPES, streetTypesValues)
  }
}

export const setAccessToken = (context: ActionContext<IState, IState>, accessToken: string) => {
  context.commit(SET_ACCESS_TOKEN, accessToken)
}
export const setLeadSell = async (
  context: ActionContext<IState, IState>,
  lead: ILeadSell
): Promise<IApiRes<ILeadSell>> => {
  return await apiShoppingcartService.postLeadSell(lead)
}

export const getLeadSell = async (
  context: ActionContext<IState, IState>,
  sellId: string
): Promise<IApiRes<ILeadSell>> => {
  return await apiShoppingcartService.getLeadSell(sellId)
}

export const setFreezeCart = async (
  context: ActionContext<IState, IState>,
  freezeCart: IfreezeCart
): Promise<void> => {
  const cart = await apiShoppingcartService.postFreezeCart(freezeCart)
  context.commit(SET_FREEZE_CART, cart.data.id)
  cart.data.id && saveFreezeCartToLocalStorage()
}

export const updateFreezeCart = async (
  context: ActionContext<IState, IState>,
  freezeCart: IfreezeCart
): Promise<void> => {
  await apiShoppingcartService.putFreezeCart(freezeCart)
}

export const saveFreezeCart = (): void => {
  const freezeCartId: string = store.getters[`${SHOPPING}/getFreezeCartId`]
  const freezeCart: number = store.getters[`${SHOPPING}/getFreezeCart`]
  if (!freezeCartId) {
    store.dispatch(`${SHOPPING}/${SET_FREEZE_CART}`, freezeCart)
  } else {
    store.dispatch(`${SHOPPING}/${UPDATE_FREEZE_CART}`, freezeCart)
    saveFreezeCartToLocalStorage()
  }
}

export const saveFreezeCartToLocalStorage = (): void => {
  const freezeCartsString: string | null = localStorage.getItem('freezeCarts')
  const freezeCarts = freezeCartsString ? JSON.parse(freezeCartsString) : []
  const freezeCartId: string = store.getters[`${SHOPPING}/getFreezeCartId`]
  const selectedFreezeCartIndex = freezeCarts.findIndex(
    (freezeCart: any) => freezeCart.id === freezeCartId
  )

  if (selectedFreezeCartIndex > -1) {
    freezeCarts[selectedFreezeCartIndex].lastModifiedDate = new Date().toISOString()
    freezeCarts[selectedFreezeCartIndex].step = store.getters[`${SHOPPING}/getCurrentStep`]
    freezeCarts[selectedFreezeCartIndex].catalog = store.getters[`${SHOPPING}/getCatalog`]
    freezeCarts[selectedFreezeCartIndex].user = store.getters[`${SHOPPING}/getNewUser`]
    localStorage.setItem('freezeCarts', JSON.stringify(freezeCarts))
  } else {
    const newFreezeCart = {
      id: freezeCartId,
      createdDate: new Date().toISOString(),
      lastModifiedDate: new Date().toISOString(),
      step: store.getters[`${SHOPPING}/getCurrentStep`],
      username: store.getters[`${SHOPPING}/getUsername`],
      company: store.getters[`${SHOPPING}/getCompany`],
      trademark: store.getters[`${SHOPPING}/getTrademark`],
      channel: store.getters[`${SHOPPING}/getChannel`],
      catalog: store.getters[`${SHOPPING}/getCatalog`],
      user: store.getters[`${SHOPPING}/getNewUser`]
    }
    freezeCarts.push(newFreezeCart)
    localStorage.setItem('freezeCarts', JSON.stringify(freezeCarts))
  }
}

export const getFreezeCart = async (
  context: ActionContext<IState, IState>,
  cartId: string
): Promise<number> => {
  const freezeCart = await apiShoppingcartService.getFreezeCart(cartId)
  if (freezeCart.status === 200) {
    context.commit(SET_FREEZE_CART, cartId)
    store.dispatch(`${SHOPPING}/${RECOVER_SELLING_DATA}`, freezeCart.data)
  }
  return freezeCart.status
}

export const getFreezeCartWithOpportunityId = async (
  context: ActionContext<IState, IState>,
  opportunityId: string
): Promise<number> => {
  const freezeCart = await apiShoppingcartService.getFreezeCartWithOpportunityId(opportunityId)
  if (freezeCart.status === 200) {
    context.commit(SET_FREEZE_CART, opportunityId)
    store.dispatch(`${SHOPPING}/${RECOVER_SELLING_DATA}`, freezeCart.data)
  }
  return freezeCart.status
}

export const deleteFreezeCart = async (
  context: ActionContext<IState, IState>,
  cartId: string
): Promise<void> => {
  await apiShoppingcartService.deleteFreezeCart(cartId)
  const freezeCartsString: string | null = localStorage.getItem('freezeCarts')
  const freezeCarts = freezeCartsString ? JSON.parse(freezeCartsString) : []
  const existingFreezeCarts = freezeCarts.filter((freezeCart: any) => freezeCart.id !== cartId)
  localStorage.setItem('freezeCarts', JSON.stringify(existingFreezeCarts))
}

export const recoverSellingData = (
  context: ActionContext<IState, IState>,
  freezeCart: IfreezeCart
): void => {
  const channel: string = store.getters[`${SHOPPING}/${GET_CHANNEL}`]
  if (channel === CHANNEL_CALL_CENTER && freezeCart.channel !== CHANNEL_CALL_CENTER) {
    store.dispatch(`${SHOPPING}/${SET_STEP}`, freezeCart.step - 1)
  } else {
    store.dispatch(`${SHOPPING}/${SET_STEP}`, freezeCart.step)
  }
  store.dispatch(`${SHOPPING}/${STEP_COMPLETED}`, freezeCart.step)
  store.dispatch(`${SHOPPING}/${SET_NEW_USER}`, freezeCart.user)
  store.dispatch(`${SHOPPING}/${SET_OLD_USER}`, freezeCart.user)
  store.dispatch(`${SHOPPING}/${SET_GDPR}`, freezeCart.user?.gdprRegistries)
  store.dispatch(`${SHOPPING}/${SET_ADV}`, freezeCart.user?.advertisingConfigurations)
  freezeCart.rateCode && store.dispatch(`${SHOPPING}/${SET_RATE_CODE}`, freezeCart.rateCode)
  freezeCart.family && store.dispatch(`${SHOPPING}/${SET_PRODUCT_FAMILY}`, freezeCart.family)
  freezeCart.billingData &&
    store.dispatch(`${SHOPPING}/${SET_BILLING_DATA}`, freezeCart.billingData)
  freezeCart.catalog?.length && store.dispatch(`${SHOPPING}/${SET_CATALOG}`, freezeCart.catalog)
  freezeCart.shippingAddress &&
    store.dispatch(`${SHOPPING}/${SET_SIM_ADDRESS}`, freezeCart.shippingAddress)
  freezeCart.billingData?.iban &&
    store.dispatch(`${SHOPPING}/${SET_BANK_ACCOUNT}`, freezeCart.billingData?.iban)
  freezeCart.billingData?.cccOwner &&
    store.dispatch(`${SHOPPING}/${SET_ACCOUNT_HOLDER}`, freezeCart.billingData?.cccOwner)
}

export const cleanSellingData = (context: ActionContext<IState, IState>): void => {
  context.commit(CLEAN_SELLING_DATA)
}

export const setBillingData = (
  context: ActionContext<IState, IState>,
  billingData: { billingData: IBillingData; isChanged: boolean }
): void => {
  context.commit(SET_BILLING_DATA, billingData)
}
export const setBillingAddress = (
  context: ActionContext<IState, IState>,
  billingData: { billingAddress: IAddressForm; isChanged: boolean }
): void => {
  context.commit(SET_BILLING_ADDRESS, billingData)
}

export const setSimAddress = (
  context: ActionContext<IState, IState>,
  address: IAddressForm
): void => {
  context.commit(SET_SIM_ADDRESS, address)
}

export const removeSimAddress = (context: ActionContext<IState, IState>): void => {
  context.commit(REMOVE_SIM_ADDRESS)
}

export const setCoverage = (context: ActionContext<IState, IState>, coverage: ICoverage): void => {
  context.commit(SET_COVERAGE, coverage)
}

export const setBankAccount = (
  context: ActionContext<IState, IState>,
  bankAccount: string
): void => {
  context.commit(SET_BANK_ACCOUNT, bankAccount)
}

export const setAccountHolder = (
  context: ActionContext<IState, IState>,
  accountHolder: object
): void => {
  context.commit(SET_ACCOUNT_HOLDER, accountHolder)
}

export const setCatalog = (context: ActionContext<IState, IState>, catalog: ICatalog[]): void => {
  context.commit(SET_CATALOG, catalog)
}

export const setAccountId = (context: ActionContext<IState, IState>, accountId: string): void => {
  context.commit(SET_ACCOUNT_ID, accountId)
}

export const setUserId = (context: ActionContext<IState, IState>, userId: string): void => {
  context.commit(SET_USER_ID, userId)
}

export const setGescal = (
  context: ActionContext<IState, IState>,
  gescal?: string | undefined
): void => {
  context.commit(SET_GESCAL, gescal)
}
