{"coverage_form": {"check_coverage": "Comprobar cobertura"}, "coverage": {"description": "Al facilitar su dirección podremos saber si dispone de cobertura"}, "personal-data": {"title": "Datos Personales", "description": "Para empezar necesitamos tus datos", "name": "Nombre", "surname": "<PERSON><PERSON><PERSON><PERSON>", "second-surname": "<PERSON><PERSON><PERSON>", "phone": "Teléfono", "phone-placeholder": "Número de contacto", "email": "Email", "email-placeholder": "Dirección de correo electrónico", "id-number": "DNI o NIE", "nationality": "Nacionalidad", "nationality-placeholder": "Selecciona tu nacionalidad", "birthdate": "Fecha de nacimiento", "birthdate-placeholder": "dd-mm-aaaa", "user": "Usuario", "password": "Contraseña", "reset-password": "Recuperar contraseña", "create-account": "<PERSON><PERSON><PERSON> cuenta", "new-customer": "Nuevo cliente", "old-customer": "Ya soy cliente", "customer-type": "Soy autónomo", "company-name": "Datos de la compañía", "cif-number": "Número de CIF", "business-name": "Razón social", "admin-name": "Nombre del administrador", "admin-surname": "Apellidos del administrador", "admin-id-number": "DNI o NIE del administrador", "admin-doc-type": "Tipo de documento", "foundation-date": "Fecha de fundación de la empresa", "login-error": "Ha ocurrido un error, no se ha podido iniciar sesión correctamente.", "particular": "Particular", "business": "Empresa"}, "streetType": {"all": "<PERSON><PERSON>", "other": "Otras", "avenue": "Aveni<PERSON>", "bulevar": "Rambla", "crossing": "Travesía", "gardens": "Jardines", "highway": "Carretera", "neighborhood": "Barrio", "passage": "Pa<PERSON>je", "park": "Parque", "patrol": "<PERSON><PERSON>", "placette": "Placeta", "polygon": "Polígono", "promenade": "Paseo", "road": "Camino", "roundabout": "Glorieta", "sector": "Sector", "settlement": "Poblado", "square": "Plaza", "street": "Calle", "suburb": "Colonia", "track": "Vía", "urbanization": "Urbanización"}, "billing-data": {"title": "Datos de facturación", "description": "Dirección de facturación", "description-shipping": "Dirección de facturación y envio", "send": "Enviar", "clear": "Bo<PERSON>r", "invalidInputValue": "El valor actual no es válido", "sim-address-message": "Quiero una dirección diferente a la dirección de facturación.", "sim": " la targeta SIM", "and": " i", "sim-address": "Dirección de envío de", "agile-address-message": "Quiero recibir el smartbox de Agile Tv en una dirección diferente a la dirección de facturación.", "agile-address": "Dirección de envío Agile Tv", "agile-title": " Agile Tv", "agile-account-email": "Email cuenta Agile Tv", "authorize-person-message": "Vull autoritzar la recollida de la targeta SIM a una altra persona.", "authorized-person": "Persona autoritzada", "owner-account": "Titular de la cuenta", "owner-account-message": "Debe coincidir con el titular del contrato", "owner-account-dni": "DNI del titular de la cuenta", "account-number": "Número de cuenta bancaria (IBAN)", "account-number-message": "El número IBAN es un código de 24 códigos que identifica tu cuenta bancaria en cualquier parte del mundo.", "invoice-communication": "Recibirás tu factura en tu correo electrónico", "discounts": "Descuentos", "select-discount": "Selecciona el descuento a aplicar"}, "resum-data": {"title": "Resumen de su comanda", "personal-data": "Datos personales", "name-surname": "Nombre y apellidos", "name-company": "Nombre de la empresa", "birthdate": "Fecha de nacimiento", "email": "Email", "phone": "Teléfono", "coverage": "Datos de instalación", "coverage-street": "Dirección de instalación", "lines": "Líneas", "fix": "<PERSON><PERSON>", "nationality": "Nacionalidad", "docNum": "Número de documento", "fiber": "Fibra", "mobile": "Móvil", "additional-mobile": "Móvil addicional", "tv": "Television", "tv-user": "Usuario television", "switchboard": "Producto de centraleta", "addon": "Producto adicional", "billing-data": "Datos de facturación", "billing-street": "Dirección de facturación", "sim": "Dirección envio", "iban": "IBAN", "price": "<PERSON><PERSON>", "product": "Productos contratados", "eur": "€/mes", "eur-without": "€/mes sin iva", "new-line": "New line", "portability": "Portabilidad de la línea", "validation-action": "Valido la información y acepto", "sim-address-check-label": "Acepto las condiciones de contratación.", "sim-address-check-label-url": "<PERSON><PERSON>.", "error": "Oops! Algo ha ido mal.", "error-later": "Vuelve a intentarlo más tarde.", "waiting-message": "Realizando pedido. Este proceso puede tardar unos cuantos minutos a completarse.", "success": "Te informamos que su pedido se ha realizado con éxito", "success-web-1": "Ya solo queda un paso!", "success-web-2": "Accede a tu correo electrónico para validar tu identidad.", "thanks": "<PERSON>as gracias!", "total-price": "Precio total", "scheduling-title": "Citaciones de fibra según dirección instalación", "scheduling-manual": "Accede a MySim para realizar la citación de instalación de la fibra", "scheduling-automatic": "En breve nos pondremos en contacto con el cliente para realizar la citación de instalación de la fibra", "scheduling-manual-new": "Es necesario que el cliente valide su identidad antes de poder realizar la citación de instalación de la fibra en MySim", "scheduling-automatic-new": "Una vez el cliente haya validado su identidad, nos pondremos en contacto con él para realizar la citación de instalación de la fibra", "product-conditions": "Condiciones del producto", "price-offer": " durante {months} meses, después {finalPrice}€/month", "authorized-person-name": "Nombre de la persona autorizada", "authorized-person-phone": "Teléfono de la persona autorizada", "customer-type": "Tipo de cliente", "CustomerResidential": "Particular", "CustomerFreelance": "Autónomo"}, "actions": {"next": "Siguient<PERSON>", "prev": "Anterior", "sign-in": "In<PERSON><PERSON>", "save": "Guardar", "edit": "<PERSON><PERSON>", "goto-main-page": "Ir a página principal", "edit-cart": "Modificar el carrito de la compra", "print-summary": "Imprimir resumen"}, "errors": {"required": "Este valor es obligatorio", "email": "El email no es válido", "phone": "El teléfono no es válido", "isValidDoc": "El documento no es válido, debe ser un NIF, NIE, CIF o pasaporte", "alpha": "Formato inválido, solo admite letras", "alphaNum": "Formato in<PERSON>, solo admite carácteres alfanuméricos", "numeric": "Formato inv<PERSON>, solo admite carácteres numéricos", "general-error": "Ha habido un error", "username": "Revisa el nombre de usuario introducido", "account": "Tu usuario no ha sido reconocido. Verifica que permites las ventanas emergentes y que has iniciado sesión correctamente."}, "stepper": {"personal-data": "Datos personales", "products": "Productos", "billing-data": "Datos de facturación", "coverage": "Cobertura", "confirmation": "Confirmación"}, "tariffForm": {"others": "Otras"}, "catalog": {"set-family-without-coverage-info": "Para ofrecerte el mejor precio, necesitamos saber donde quieres instalar la fibra.", "set-family-without-coverage-answer": "¿Quieres continuar?", "confirm-set-coverage": "Si", "cancel-set-coverage": "No"}, "banner": {"price": "Precio", "mobile": "Móvil", "fixFiber": "Fijo + Fibra"}}