<template>
  <!-- Dad<PERSON> personals -->
  <main v-if="!isValidated && !loading">
    <!-- Producte Contractat -->
    <div id="print-content">
      <PwSeparator :title="`${t('resum-data.title')}`"></PwSeparator>
      <div class="px-4">
        <section v-if="catalogDataArray?.length" class="mb-6">
          <div class="flex mb-4 pt-6 justify-between border-b border-primary w-full items-center">
            <h4 class="text-base font-bold">{{ t('resum-data.product') }}</h4>
            <router-link
              :to="{ name: 'products' }"
              @click="isChannelWeb ? goToStep('2') : goToStep('1')"
              class="flex items-center text-primary"
              ><span class="text-sm z-10 -mr-2">{{ t('actions.edit-cart') }}</span
              ><img :src="editIcon" width="50" alt="Edit productes contractats"
            /></router-link>
          </div>
          <div
            v-for="(catalogData, index) of catalogDataArray"
            :key="index"
            v-show="catalogData.tariffRate"
            class="text-sm"
          >
            <div class="flex mt-5 justify-between bg-primary-light p-1 px-3 rounded">
              <p>{{ catalogData.tariffRate?.data.name.toUpperCase() }}</p>
              <div
                v-if="
                  (catalogData.tariffRate?.offerPrice ||
                    catalogData.tariffRate?.offerPrice === 0) &&
                  catalogData.tariffRate?.durationDaysPromotionalPrize
                "
              >
                <p>
                  <span class="mr-2">{{ t('resum-data.price') }}:</span
                  ><span class="font-bold"
                    >{{ price[index].offer.toFixed(2) }}{{ t('resum-data.eur') }}</span
                  >{{
                    t('resum-data.price-offer', {
                      months: catalogData.tariffRate.durationDaysPromotionalPrize,
                      finalPrice: setProductPrice(catalogData, 'final')
                    })
                  }}
                </p>
                <p class="text-xs text-dark">
                  {{ (price[index].offer * 0.79).toFixed(2) }}{{ t('resum-data.eur-without') }}
                </p>
              </div>
              <div v-else>
                <p>
                  <span class="mr-2">{{ t('resum-data.price') }}:</span
                  ><span class="font-bold"
                    >{{ price[index].final.toFixed(2) }}{{ t('resum-data.eur') }}</span
                  >
                </p>
                <p class="text-xs text-dark">
                  {{ (price[index].final * 0.79).toFixed(2) }}{{ t('resum-data.eur-without') }}
                </p>
              </div>
            </div>
            <div
              v-for="(product, productIndex) of catalogData.contractedProducts"
              :key="productIndex"
              class="px-3"
            >
              <div v-if="product.fiber" class="pt-3 flex">
                <img :src="wifiIcon" width="22" :alt="t('resum-data.fiber')" class="mr-2" />
                <div>
                  <p>
                    <span class="font-bold mr-1">{{ t('resum-data.fiber') }}:</span>
                    {{ product.fiber.fibSpeed }} Mb
                  </p>
                  <p>
                    <span class="font-bold mr-1">{{ t('resum-data.coverage-street') }}:</span
                    >{{ setAddressString(product.fiber.installationAddress) }}
                  </p>
                </div>
              </div>
              <p v-else-if="product.landLine" class="pt-3 flex">
                <img :src="telephoneIcon" width="20" :alt="t('resum-data.fix')" class="mr-2" /><span
                  class="font-bold mr-1"
                  >{{ t('resum-data.fix') }}:</span
                >
                {{
                  product.landLine.portability
                    ? `Portabilitat de la línia ${product.landLine.number}`
                    : 'Linia Nova'
                }}
              </p>
              <p v-else-if="product.mobile" class="pt-3 flex">
                <img :src="simIcon" width="20" :alt="t('resum-data.mobile')" class="mr-2" /><span
                  class="font-bold mr-1"
                  >{{ t('resum-data.mobile') }}:</span
                >
                {{
                  product.mobile.portability
                    ? `Portabilitat de la línia ${product.mobile.numberInfo.number}`
                    : 'Linia Nova'
                }}
              </p>
              <p v-else-if="product.tv" class="pt-3 flex">
                    <div class="flex" v-if="catalogData.tariffRate?.products?.[productIndex]?.specifications
                    ?.provisioningSubClass === 'TVService'">
                      <img :src="tvIcon" width="20" :alt="t('resum-data.tv')" class="mr-2" /><span
                        class="font-bold mr-1"
                        >{{ t('resum-data.tv-user') }}:</span
                      >
                      {{ product.tv.tvUser }}

                    </div>
              </p>
              <p
                v-else-if="
                  catalogData.tariffRate?.products?.[productIndex]?.specifications
                    ?.provisioningClass === 'Switchboard'
                "
                class="pt-3 flex"
              >
                <img
                  :src="switchboardIcon"
                  width="20"
                  :alt="t('resum-data.switchboard')"
                  class="mr-2"
                /><span class="font-bold mr-1">{{ t('resum-data.switchboard') }}:</span>
                {{ catalogData.tariffRate?.data.name }}
              </p>
              <div v-else class="pt-3 flex flex-col">
              <div class="flex items-center">
                <div class="flex items-center justify-center mr-1">
                  <img :src="addonIcon" width="20" :alt="t('resum-data.addon')" class="mr-1" />
                </div>
                <div class="flex flex-col">
                  <div class="flex items-center mb-1">
                    <span class="font-bold mr-1">{{ t('resum-data.addon') }}:</span>
                    <span>{{ catalogData.tariffRate?.data.name }}</span>
                  </div>
                  <div v-if="catalogData.tariffRate?.data?.specifications?.digitalServiceNumber">
                    <span class="font-bold mr-1">{{ t('resum-data.phone') }}:</span>
                    <span>{{ catalogData.tariffRate?.data?.specifications?.digitalServiceNumber }}</span>
                  </div>
                </div>
              </div>
            </div>
            </div>
            <div class="text-sm px-3 mt-6">
              <p
                v-if="catalogData.tariffRate?.charges?.length"
                class="mb-1 text-sm text-primary font-semibold"
              >
                {{ t('resum-data.product-conditions') }}:
              </p>
              <p v-for="(charge, index) of catalogData.tariffRate?.charges" :key="index + 100">
                - {{ charge.description }}. {{ t('resum-data.price') }}:
                {{ charge.penaltyPrice.toFixed(2) }}€
              </p>
            </div>
          </div>
          <div class="flex text-lg mt-8 justify-end px-3 bg-primary p-1 w-full rounded">
            <p>{{ t('resum-data.total-price') }}:</p>
            <div class="flex flex-col items-end">
              <p class="font-bold ml-2">{{ totalPrice.toFixed(2) }} {{ t('resum-data.eur') }}</p>
              <p class="text-xs text-dark ml-2">
                {{ (totalPrice * 0.79).toFixed(2) }} {{ t('resum-data.eur-without') }}
              </p>
            </div>
          </div>
        </section>
      </div>
      <!-- DADES PARTICULAR -->
      <div v-if="personalData && personalData.documentType !== 'CIF'">
        <section class="mb-6 px-4">
          <div class="flex mb-4 pt-6 justify-between border-b border-primary w-full items-center">
            <h4 class="text-base font-bold">{{ t('resum-data.personal-data') }}</h4>
            <router-link
              :to="{ name: 'personal-data' }"
              @click="isChannelWeb ? goToStep('1') : goToStep('2')"
              class="flex items-center text-primary"
              ><span class="text-sm z-10 -mr-2">{{ t('actions.edit') }}</span
              ><img :src="editIcon" width="50" alt="Edit personal data"
            /></router-link>
          </div>

          <div class="grid @xs:grid-cols-3 @2xl:grid-cols-6">
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.name-surname') }}</p>
            <p class="pt-3 col-span-2">{{ personalData.firstName }} {{ personalData.lastName }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.birthdate') }}</p>
            <p class="pt-3 col-span-2">{{ formattedDate }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.docNum') }}</p>
            <p class="pt-3 col-span-2">{{ personalData.documentNumber }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.customer-type') }}</p>
            <p class="pt-3 col-span-2">
              {{ personalData.customerType ? t(`resum-data.${personalData.customerType}`) : '-' }}
            </p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.nationality') }}</p>
            <p class="pt-3 col-span-2">
              {{
                countries?.find((d: IPickListValue) => d.value == personalData?.nationality)
                  ?.label || personalData.nationality
              }}
            </p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.email') }}</p>
            <p class="pt-3 col-span-2">{{ contactData?.email }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.phone') }}</p>
            <p class="pt-3 col-span-2">{{ contactData?.phone }}</p>
          </div>
        </section>
      </div>
      <!-- DADES EMPRESA -->
      <div v-else>
        <section
          class="mb-6 px-4"
          v-if="personalData && companyStructure && personalData.documentType === 'CIF'"
        >
          <div class="flex mb-4 pt-6 justify-between border-b border-primary w-full items-center">
            <h4 class="text-base font-bold">{{ t('personal-data.company-name') }}</h4>
            <router-link
              :to="{ name: 'personal-data' }"
              @click="isChannelWeb ? goToStep('1') : goToStep('2')"
              class="flex items-center text-primary"
              ><span class="text-sm z-10 -mr-2">{{ t('actions.edit') }}</span
              ><img :src="editIcon" width="50" alt="Edit company data"
            /></router-link>
          </div>
          <div class="grid @xs:grid-cols-3 @2xl:grid-cols-6">
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.name-company') }}</p>
            <p class="pt-3 col-span-2">{{ personalData.completeName }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('personal-data.foundation-date') }}</p>
            <p class="pt-3 col-span-2">{{ formattedDateBusiness }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('personal-data.cif-number') }}</p>
            <p class="pt-3 col-span-2">{{ personalData.documentNumber }}</p>

            <p class="mr-2 pt-3 font-bold text-sm">{{ t('personal-data.nationality') }}</p>
            <p class="pt-3 col-span-2">
              {{
                countries?.find((d: IPickListValue) => d.value == personalData?.nationality)
                  ?.label || personalData.nationality
              }}
            </p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.email') }}</p>
            <p class="pt-3 col-span-2">{{ contactData?.email }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.phone') }}</p>
            <p class="pt-3 col-span-2">{{ contactData?.phone }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('personal-data.admin-name') }}</p>
            <p class="pt-3 col-span-2">{{ companyStructure.companyManagerFirstName }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('personal-data.admin-surname') }}</p>
            <p class="pt-3 col-span-2">{{ companyStructure.companyManagerLastName }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">
              {{ t('personal-data.admin-doc-type') }}
            </p>
            <p class="pt-3 col-span-2">{{ companyStructure.administratorDocumentType }}</p>
            <p class="mr-2 pt-3 font-bold text-sm">{{ t('personal-data.admin-id-number') }}</p>
            <p class="pt-3 col-span-2">{{ companyStructure.administratorDocumentNumber }}</p>
          </div>
        </section>
      </div>

      <!-- Dades de facturació -->
      <section v-if="billingData" class="mb-6 px-4">
        <div class="flex mb-4 pt-6 justify-between border-b border-primary w-full items-center">
          <h4 class="text-base font-bold">{{ t('resum-data.billing-data') }}</h4>
          <router-link
            :to="{ name: 'billing-data' }"
            @click="goToStep('3')"
            class="flex items-center text-primary"
            ><span class="text-sm z-10 -mr-2">{{ t('actions.edit') }}</span
            ><img :src="editIcon" width="50" alt="Edit personal data"
          /></router-link>
        </div>
        <div class="grid @xs:grid-cols-3 @2xl:grid-cols-6">
          <p v-if="billingData.cccOwner" class="mr-2 pt-3 font-bold text-sm">
            {{ t('billing-data.owner-account') }}
          </p>
          <p v-if="billingData.cccOwner" class="pt-3 col-span-2">{{ billingData.cccOwner }}</p>
          <p v-if="billingData.cccOwnerIdentification" class="mr-2 pt-3 font-bold text-sm">
            {{ t('billing-data.owner-account-dni') }}
          </p>
          <p v-if="billingData.cccOwnerIdentification" class="pt-3 col-span-2">
            {{ billingData.cccOwnerIdentification }}
          </p>
          <p class="mr-2 pt-3 font-bold text-sm">{{ t('billing-data.account-number') }}</p>
          <p class="pt-3 col-span-2">{{ billingData.iban }}</p>
          <p class="mr-2 pt-3 font-bold text-sm">{{ t('resum-data.billing-street') }}</p>
          <p class="pt-3 col-span-2">{{ setAddressString(billingAddress) }}</p>
          <div v-if="showSimAddressBlock" class="grid grid-cols-3 col-span-3">
            <p class="mr-2 font-bold pt-3 text-sm">{{ t('resum-data.sim') }}:</p>
            <div v-if="!isCheckedSimAddress" class="pt-3 col-span-2">
              <p>
                {{ setAddressString(billingAddress) }}
              </p>
            </div>
            <div v-else class="pt-3 col-span-2">
              <div>{{ setAddressString(shippingAddress) }}</div>
            </div>
          </div>
          <p v-if="selectedDiscount?.value" class="mr-2 pt-3 font-bold text-sm">
            {{ t('billing-data.discounts') }}
          </p>
          <p v-if="selectedDiscount?.value" class="pt-3 col-span-2">{{ selectedDiscount.label }}</p>
          <!-- <p v-if="authorizedPersonData.name" class="mr-2 pt-3 font-bold text-sm">
            {{ t('resum-data.authorized-person-name') }}
          </p>
          <p v-if="authorizedPersonData.name" class="pt-3 col-span-2">
            {{ authorizedPersonData.name }}
          </p>
          <p v-if="authorizedPersonData.phone" class="mr-2 pt-3 font-bold text-sm">
            {{ t('resum-data.authorized-person-phone') }}
          </p>
          <p v-if="authorizedPersonData.phone" class="pt-3 col-span-2">
            {{ authorizedPersonData.phone }}
          </p> -->
        </div>
      </section>
    </div>
    <div class="pb-8 px-4">
      <div class="flex mb-4 pt-6 justify-between w-full items-center">
        <div class="flex items-center min-w-1/2">
          <PwCheck
            :lang="lang"
            name="shippingAddress"
            v-model="checkedTerms"
            :checked="checkedTerms"
            labelClass="text-secondary"
            :label="t('resum-data.sim-address-check-label')"
          />
          <a
            target="_blank"
            href="https://parlem.com/wp-content/uploads/2022/10/CGCT_PR22_01-Cat.pdf"
            class="underline cursor-pointer ml-2"
            >{{ t('resum-data.sim-address-check-label-url') }}</a
          >
        </div>
        <span
          type="button"
          @click="print"
          class="cursor-pointer text-secondary flex justify-end w-[180px] mr-4"
        >
          <font-awesome-icon icon="fa-solid fa-print" class="max-w-[16px] mr-2" />
          <p>{{t('actions.print-summary')}}</p>
        </span>
      </div>
      <div class="flex justify-between items-center">
        <PwButton
          @click="prevStep"
          :disabled="isSending"
          theme="light"
          class="w-[60px] max-w-[60px] h-[40px] mr-2"
        >
          <font-awesome-icon icon="fa-solid fa-angle-left" class="max-w-[15px] text-gray" />
        </PwButton>
        <PwButton
          :text="isSending ? t('resum-data.waiting-message') : t('resum-data.validation-action')"
          :disabled="!checkedTerms || isDisabledButton"
          @click="validate"
        >
          <font-awesome-icon
            v-if="isSending"
            icon="fa-solid fa-spinner"
            class="max-w-[20px] text-info mr-2"
          />
        </PwButton>
      </div>
      <div v-if="showErrorValidated" class="flex flex-col justify-center">
        <div class="flex justify-center">
          <font-awesome-icon
            icon="fa-solid fa-triangle-exclamation"
            class="max-w-[15px] text-error mr-1"
          />
          <p class="text-error font-bold">
            {{ t('resum-data.error') }}
          </p>
        </div>
        <p v-if="sellLeadApiResponseError.length" class="text-error text-center mt-3 font-bold">
          {{ sellLeadApiResponseError }}
        </p>
      </div>
    </div>
  </main>
  <section v-if="isValidated && !loading">
    <PwSeparator :title="isValidated ? '' : `${t('resum-data.title')}`"></PwSeparator>
    <div class="flex flex-col items-center min-h-[50vh] justify-between m-10 raleway">
      <img :src="successIcon" alt="Success validation" />
      <div class="flex flex-col items-center">
        <p v-if="isUnconfirmed && isNewClient" class="font-medium text-2xl text-center mt-8">
          {{ t('resum-data.success-web-1') }}
        </p>
        <p v-if="isUnconfirmed && isNewClient" class="font-bold text-2xl text-center mt-2 mb-8">
          {{ t('resum-data.success-web-2') }}
        </p>
        <p v-else class="font-bold text-2xl text-center my-4">{{ t('resum-data.success') }}</p>
        <p class="font-bold text-primary text-4xl mb-9">{{ t('resum-data.thanks') }}</p>
        <div v-if="sellLeadSchedulings.length" class="mt-2 mb-8 py-3 w-2/3">
          <p class="font-bold text-xl text-center">{{ t('resum-data.scheduling-title') }}</p>
          <div v-for="(scheduling, index) of sellLeadSchedulings" :key="index" class="text-center">
            <p class="font-bold mt-4">{{ index + 1 }}. {{ scheduling.address }}:</p>
            <p class="">
              {{
                scheduling.needManualSchedule
                  ? isNewClient
                    ? t('resum-data.scheduling-manual-new')
                    : t('resum-data.scheduling-manual')
                  : isNewClient
                  ? t('resum-data.scheduling-automatic-new')
                  : t('resum-data.scheduling-automatic')
              }}
            </p>
          </div>
        </div>
      </div>
      <PwButton
        :text="t('actions.goto-main-page')"
        :disabled="false"
        @click="goToMainPage"
      ></PwButton>
    </div>
  </section>
  <div v-if="loading" class="flex justify-center mt-10">
    <font-awesome-icon icon="fa-solid fa-spinner" class="max-w-[20px] text-info" />
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import type { Ref } from 'vue'
import { useStore } from 'vuex'
import en from '@/i18n/locales/en.json'
import es from '@/i18n/locales/es.json'
import ca from '@/i18n/locales/ca.json'
import gl from '@/i18n/locales/gl.json'
import { useI18n } from 'vue-i18n'
import { PwSeparator, PwButton, PwCheck } from 'parlem-webcomponents-common'
import type { IBillingData } from '@/services/users/interfaces/billing-data.interface'
import {
  SHOPPING,
  SET_LEAD_SELL,
  GET_STEPS,
  GET_ACCOUNT_ID,
  GET_LEAD_SELL,
  DELETE_FREEZE_CART,
  GET_TRADEMARK,
  GET_SELECTED_DISCOUNT
} from '@/store/modules/shopping/constants/store.constants'
import { leadsService, stepperService } from '@/services'
import type { IAddressForm } from '../billing-data/interfaces/address-form.interface'
import type { ILeadSell } from '@/services/api/shoppingcart/interfaces/lead-sell.interface'
import type { IAddress, IContact, IPersonalData, IUser } from '@/services/users/interfaces'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import type { ICatalog } from '@/interfaces/catalog.interface'
import type { ILeadPersonalData } from '@/services/api/shoppingcart/interfaces/lead-personal-data.interface'
import type { ILeadBillingInfo } from '@/services/api/shoppingcart/interfaces/lead-billing-info.interface'
import type { ILeadContractedRates } from '@/services/api/shoppingcart/interfaces/lead-contracted-rates.interface'
import type { IRateContractedProduct } from '@/services/api/shoppingcart/interfaces/rate-contracted-product.interface'
import type { IPickListValue } from '@/services/api/crm/interfaces'
import type { IStep } from '@/interfaces/step.interface'
import type { ILeadCandidate } from '@/interfaces/lead-candidate'
import type { ILeadTerms } from '@/services/api/shoppingcart/interfaces/lead-terms.interface'
import { CHANNEL_CALL_CENTER, CHANNEL_WEB } from '@/services/constants/services.constants'
import type { IAdditonalMobile } from '@/interfaces/catalog.interface'
import type { IApiRes } from '@/services/api/interfaces'
import { setAddressString, setAddressToSend } from 'parlem-webcomponents-common'
import type { ILeadSellApiResponse } from '@/services/api/shoppingcart/interfaces/lead-sell-api-response.interface'
import type { IShadowElement } from '@/interfaces/shadowElement.interface'

export default defineComponent({
  name: 'ResumData',
  components: {
    PwSeparator,
    PwButton,
    PwCheck
  },
  setup(props: any) {
    const store = useStore()
    const { t, locale }: any = useI18n({
      messages: {
        en,
        gl,
        es,
        ca
      }
    })
    const trademark: Ref<string> = computed(() => store.getters[`${SHOPPING}/${GET_TRADEMARK}`])
    const editIcon = `${import.meta.env.VITE_ICONS_URL}/edit-${trademark.value.toLowerCase()}.svg`
    const successIcon = `${
      import.meta.env.VITE_ICONS_URL
    }/success-${trademark.value.toLowerCase()}.svg`
    const wifiIcon = `${import.meta.env.VITE_ICONS_URL}/wifi-black.svg`
    const telephoneIcon = `${import.meta.env.VITE_ICONS_URL}/telephone-black.svg`
    const simIcon = `${import.meta.env.VITE_ICONS_URL}/sim-black.svg`
    const tvIcon = `${import.meta.env.VITE_ICONS_URL}/tv-black.svg`
    const switchboardIcon = `${import.meta.env.VITE_ICONS_URL}/switchboard-black.svg`
    const addonIcon = `${import.meta.env.VITE_ICONS_URL}/addon-black.svg`
    const lang: Ref<string> = computed(() => store.getters[`${SHOPPING}/getLang`])

    locale.value = lang.value || locale.value
    return {
      t,
      store,
      editIcon,
      successIcon,
      wifiIcon,
      telephoneIcon,
      simIcon,
      switchboardIcon,
      tvIcon,
      addonIcon
    }
  },
  data(): any {
    return {
      isValidated: false,
      isUnconfirmed: false,
      checkedTerms: false,
      isDisabledButton: false,
      showErrorValidated: false,
      isSending: false,
      validateSellTimeout: 2000,
      validateSellMaxTimeoutExecutions: 150, // if validateSellTimeout is 2000 (2s) 2*60 = 2 minutes
      validateSellCurrentTimeOutExecutions: 0,
      sellLeadApiResponseError: '',
      price: [],
      total: [],
      loading: false,
      sellLeadSchedulings: []
    }
  },
  computed: {
    formattedDate(): string {
      const date: string | undefined = this.personalData?.personBirthdate
      const formatted: string = date ? new Date(date).toLocaleDateString('es-ES') : ''
      return formatted
    },
    formattedDateBusiness(): string {
      const date: string | undefined = this.personalData?.foundationDate
      const formatted: string = date ? new Date(date).toLocaleDateString('es-ES') : ''
      return formatted
    },
    billingData(): IBillingData {
      return this.store.getters[`${SHOPPING}/getBillingData`]
    },
    billingDataIsChanged(): boolean {
      return this.store.state[SHOPPING].billingDataIsChanged
    },
    bankAccountIsChanged(): boolean {
      return this.store.state[SHOPPING].bankAccountIsChanged
    },
    accountHolderIsChanged(): boolean {
      return this.store.state[SHOPPING].accountHolderIsChanged
    },
    selectedDiscount(): IPickListValue | null {
      return this.store.getters[`${SHOPPING}/${GET_SELECTED_DISCOUNT}`]
    },
    billingAddress(): IAddressForm {
      return (
        this.billingData.billingAddress ||
        this.store.getters[`${SHOPPING}/getDefaultBillingAddress`]
      )
    },
    shippingAddress(): IAddressForm {
      return (
        this.store.getters[`${SHOPPING}/getSimAddress`] ||
        this.store.getters[`${SHOPPING}/getDefaultShippingAddress`]
      )
    },
    coverageData(): ICoverage | null {
      return this.store.getters[`${SHOPPING}/getCoverage`]
    },
    isCheckedSimAddress(): ICoverage | null {
      return this.store.getters[`${SHOPPING}/getCheckedSimAddress`]
    },
    catalogDataArray(): ICatalog[] | null {
      return this.store.getters[`${SHOPPING}/getCatalog`]
    },
    userData(): IUser {
      return this.store.getters[`${SHOPPING}/getNewUser`]
    },
    gdprRegistries(): ILeadTerms {
      const gdprRegistries = this.userData?.gdprRegistries
      return Array.isArray(gdprRegistries) ? gdprRegistries[0] : gdprRegistries
    },
    advertisingConfigurations(): ILeadTerms[] {
      return this.userData?.advertisingConfigurations
    },
    oldClient(): IUser {
      return this.store.getters[`${SHOPPING}/getOldUser`]
    },
    isNewClient(): boolean {
      return !this.oldClient
    },
    isChannelWeb() {
      return this.channel === CHANNEL_WEB
    },
    isChannelCallCenter() {
      return this.channel === CHANNEL_CALL_CENTER
    },
    personalData(): IPersonalData | undefined {
      return this.userData?.personalData
    },
    companyStructure(): IPersonalData | undefined {
      return this.userData?.companyStructure
    },
    contactData(): IContact | undefined {
      const contactsArray: IContact[] = this.userData?.provisionContacts || []
      return contactsArray.find((contact: IContact) => contact.isDefault)
    },
    countries(): IPickListValue[] {
      return this.store.getters[`${SHOPPING}/getCountries`]
    },
    channel(): string {
      return this.store.getters[`${SHOPPING}/getChannel`]
    },
    company(): string {
      return this.store.getters[`${SHOPPING}/getCompany`]
    },
    trademark(): string {
      return this.store.getters[`${SHOPPING}/getTrademark`]
    },
    username(): string {
      return this.store.getters[`${SHOPPING}/getUsername`]
    },
    dateNow(): string {
      const date = new Date()
      return new Date(date.getTime() - date.getTimezoneOffset() * 60000).toISOString()
    },
    customerId(): string {
      return ''
    },
    showSimAddressBlock(): boolean {
      return this.catalogDataArray.some(
        (rate: any) =>
          rate.contractedProducts.find(
            (product: any) => product.mobile || product.additionalMobileLines?.length
          ) ||
          this.catalogDataArray.some((rate: any) =>
            rate.contractedProducts.find((product: any) => product.tv)
          )
      )
    },

    sellObject() {
      const defaultContactData = {
        name: this.companyStructure?.administratorDocumentNumber
          ? `${this.companyStructure.companyManagerFirstName} ${this.companyStructure.companyManagerLastName}`
          : `${this.personalData?.firstName} ${this.personalData?.lastName}`,
        email: this.contactData?.email,
        phone: this.contactData?.phone
      }

      const provisionContact = defaultContactData

      return {
        company: this.company,
        trademark: this.trademark,
        terms: {
          accepted: this.checkedTerms,
          acceptanceDate: this.checkedTerms ? this.dateNow : null,
          endAcceptanceDate: this.checkedTerms ? null : this.dateNow,
          info: null
        },
        soldby: this.username,
        soldAt: this.dateNow,
        customDiscount: this.selectedDiscount.value,
        customerId: !this.isNewClient ? this.oldClient.id : '',
        lead: {
          provisionContact,
          gdprRegistry: this.gdprRegistries,
          advertisingConfigurations: this.advertisingConfigurations,
          personalData: this.personalDataObject,
          billingAddress: this.addressObject(this.billingAddress),
          shippingAddress: this.isCheckedSimAddress
            ? this.addressObject(this.shippingAddress)
            : this.addressObject(this.billingAddress),
          billingInfo: this.billingInfoObject,
          companyStructure: {
            administratorDocumentNumber: this.companyStructure?.administratorDocumentNumber,
            administratorDocumentType: this.companyStructure?.administratorDocumentType,
            companyManagerFirstName: this.companyStructure?.companyManagerFirstName,
            companyManagerLastName: this.companyStructure?.companyManagerLastName
          }
        },
        contractedRates: this.contractedRates
      }
    },
    personalDataObject(): ILeadPersonalData {
      return {
        gender: this.personalData?.gender || null,
        firstName: this.personalData?.firstName,
        lastName: this.personalData?.lastName,
        documentType: this.personalData?.documentType || this.setDocumentType || null,
        documentNumber: this.personalData?.documentNumber || null,
        nationality: this.personalData?.nationality || null,
        completeName: this.personalData?.completeName || null,
        foundationDate: this.personalData?.foundationDate || null,
        personBirthdate: this.personalData?.personBirthdate || null,
        customerType: this.personalData?.customerType || 'CustomerResidential',
        category: this.personalData?.category || null
      }
    },

    setDocumentType(): string {
      const isNumber = /^\d$/.test(this.personalData?.documentNumber[0])
      return isNumber ? 'DNI' : 'NIE'
    },
    billingInfoObject(): ILeadBillingInfo {
      const name = this.companyStructure?.administratorDocumentNumber
        ? `${this.companyStructure.companyManagerFirstName} ${this.companyStructure.companyManagerLastName}`
        : `${this.personalData?.firstName} ${this.personalData?.lastName}`

      return {
        name,
        cccOwner: this.billingData.cccOwner,
        cccOwnerIdentification:
          this.billingData.cccOwnerIdentification || this.personalData?.documentNumber || null,
        iban: this.billingData.iban,
        sendBill: 'Email'
      }
    },

    contractedRates(): ILeadContractedRates[] {
      const rates: IRateContractedProduct[] | ILeadContractedRates[] = []
      this.catalogDataArray.map((tariff: IRateContractedProduct) => {
        rates.push({ ...tariff })
      })
      rates.forEach((tariff: IRateContractedProduct) => {
        tariff.soldby = this.username
        delete tariff.tariffRate
        tariff.contractedProducts?.forEach((product: any) => {
          if (product.fiber) {
            product.fiber.installationAddress = { ...product.fiber.installationAddress }
            product.fiber.installationAddress = this.addressObject(
              product.fiber.installationAddress
            )
          }
        })
      })
      return rates
    },
    totalPrice(): number {
      let finalPrice = 0
      this.total?.forEach((totalPrice: any) => {
        finalPrice =
          totalPrice?.offerPrice || totalPrice.offerPrice === 0
            ? finalPrice + totalPrice.offerPrice
            : totalPrice?.finalPrice
            ? finalPrice + totalPrice?.finalPrice
            : finalPrice
      })
      return finalPrice
    }
  },

  watch: {
    catalogDataArray() {
      this.setPrices()
    }
  },
  created() {
    this.loading = true
    this.setPrices()
    this.loading = false
  },

  methods: {
    print() {
      const shopping: IShadowElement = document.getElementsByTagName('pw-shopping')
      const element: any = shopping[0]?.shadowRoot
      let printContent = element
        ? element.getElementById('print-content')?.innerHTML
        : this.$refs?.['print-content']

      fetch('https://storage.googleapis.com/parlem-dev-cdn/shoppingcart/style.css')
        .then((response) => response.text())
        .then((css) => {
          printContent = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Resum Comanda</title>
          <link rel="icon" type="image/png" href="https://parlem.com/wp-content/uploads/2020/04/cropped-favicon-parlem-1.png">
          <style>
            ${css}
          </style>
        </head>
        <body>
          <div class="@container">${printContent}</div>
        </body>
        </html>
      `
          const printWindow = window.open('', '_blank')
          if (printWindow) {
            printWindow.document.write(printContent)
            printWindow.document.close()

            printWindow.onload = () => {
              printWindow.print()
              printWindow.close()
            }
          }
        })
        .catch((error) => {
          console.error('Error loading CSS file:', error)
        })
    },
    setAddressString(address: ICoverage | IAddress) {
      if (address) {
        return setAddressString(address, this.lang)
      }
    },
    setPrices() {
      this.catalogDataArray?.forEach((catalogData: ICatalog) => {
        const catalogPrice: any = {}
        let totalPrice: any = {}
        if (
          catalogData.tariffRate?.offerPrice ||
          (catalogData.tariffRate?.offerPrice === 0 &&
            catalogData.tariffRate?.durationDaysPromotionalPrize)
        ) {
          catalogPrice.offer = this.setProductPrice(catalogData, 'offer')
          totalPrice.offerPrice = catalogPrice.offer
        }
        catalogPrice.final = this.setProductPrice(catalogData, 'final')
        totalPrice.finalPrice = catalogPrice.final
        this.total.push(totalPrice)
        this.price.push(catalogPrice)
      })
    },
    setProductPrice(catalogData: ICatalog, priceType: string): number {
      let price = 0
      if (catalogData?.tariffRate) {
        if (priceType === 'offer') {
          price = price + (catalogData?.tariffRate.offerPrice || 0)
        } else {
          price = price + (catalogData?.tariffRate.finalPrice || 0)
        }
      }
      if (catalogData?.additionalMobileLines?.length) {
        catalogData.additionalMobileLines.forEach((product: IAdditonalMobile) => {
          price = price + product.price
        })
      }
      if (catalogData?.tv) {
        price = price + catalogData?.tv.price
      }
      return price
    },
    checkLeadSellStatus(sellId: string): void {
      this.store
        .dispatch(`${SHOPPING}/${GET_LEAD_SELL}`, sellId)
        .then((sellLeadAPiResponse: IApiRes<ILeadSellApiResponse>) => {
          this.validateSellApiResponse(sellLeadAPiResponse, this.validateSellTimeout)
        })
        .catch((error: any) => {
          this.isSending = false
          this.showErrorValidated = true
        })
    },
    validateSellApiResponse(
      sellLeadAPiResponse: IApiRes<ILeadSellApiResponse>,
      timeout: number = 0
    ): void {
      const sellLeadState: string = sellLeadAPiResponse.data.result.state
      const sellLeadCustomerId: string = sellLeadAPiResponse.data.customerId || ''

      if (
        ['processed', 'failed', 'canceled', 'unconfirmed', 'scoringerror'].includes(sellLeadState.toLowerCase())
      ) {
        this.isSending = false
        this.isValidated =
          sellLeadState.toLowerCase() === 'processed' ||
          sellLeadState.toLowerCase() === 'unconfirmed'
        this.isUnconfirmed = sellLeadState.toLowerCase() === 'unconfirmed'
        if (['failed', 'canceled', 'scoringerror'].includes(sellLeadState.toLowerCase())) {
          this.showErrorValidated = true
          this.sellLeadApiResponseError = sellLeadAPiResponse.data.result.errors[0]?.reason || ''
        }
        this.submitShoppingCart(sellLeadAPiResponse.data.id, sellLeadCustomerId, sellLeadState)
      } else if (
        this.validateSellCurrentTimeOutExecutions >= this.validateSellMaxTimeoutExecutions
      ) {
        this.validateSellCurrentTimeOutExecutions = 0
        this.isSending = false
        this.showErrorValidated = true
        this.submitShoppingCart(sellLeadAPiResponse.data.id, sellLeadCustomerId, sellLeadState)
      } else {
        const checkLeadSellStatus = this.checkLeadSellStatus
        const sellId = sellLeadAPiResponse.data.id

        setTimeout(() => {
          this.validateSellCurrentTimeOutExecutions++
          checkLeadSellStatus(sellId)
        }, timeout)
      }
    },
    async validate(): Promise<void> {
      this.isDisabledButton = true
      this.isSending = true
      this.showErrorValidated = false

      const sellLeadAPiResponse: IApiRes<ILeadSellApiResponse> = await this.store.dispatch(
        `${SHOPPING}/${SET_LEAD_SELL}`,
        this.sellObject
      )
      if ([200, 201].includes(sellLeadAPiResponse.status)) {
        if (this.isChannelWeb) {
          this.isValidated = true
          const sellLeadState = sellLeadAPiResponse.data.result.state
          const sellLeadCustomerId = sellLeadAPiResponse.data.customerId || ''
          this.submitShoppingCart(sellLeadAPiResponse.data.id, sellLeadCustomerId, sellLeadState)
        } else {
          this.sellLeadSchedulings = this.isChannelCallCenter
            ? (this.sellLeadSchedulings = sellLeadAPiResponse.data.result?.schedulings || [])
            : []
          this.validateSellApiResponse(sellLeadAPiResponse)
        }
      } else {
        this.isSending = false
        this.showErrorValidated = true
        this.sellLeadApiResponseError = sellLeadAPiResponse?.data || ''
      }
    },
    submitShoppingCart(sellId: string, customerId: string, state: string) {
      state !== 'Failed' && state !== 'Pending' && this.createLeadCandidate()
      const contractObject = { sellId, customerId, state }
      const submitShoppingCartEvent = new CustomEvent('submit-shopping-cart-event', {
        detail: contractObject
      })
      window.dispatchEvent(submitShoppingCartEvent)
      this.deleteFreezeCart()
    },
    deleteFreezeCart(): void {
      const freezeCartId: number = this.store.getters[`${SHOPPING}/getFreezeCartId`]
      this.store.dispatch(`${SHOPPING}/${DELETE_FREEZE_CART}`, freezeCartId)
    },
    goToMainPage(): void {
      if (this.isChannelWeb) {
        window.location.replace('http://parlem.com')
      } else {
        location.reload()
      }
    },
    prevStep(): void {
      stepperService.prevStep()
    },
    goToStep(step: string): void {
      const steps: IStep[] = this.store.getters[`${SHOPPING}/${GET_STEPS}`]
      const findStep: IStep | undefined = steps.find((st: IStep) => st.label === step)
      if (findStep) {
        stepperService.gotToStep(findStep)
      }
    },
    addressObject(address: IAddressForm): any {
      return setAddressToSend(address)
    },
    async createLeadCandidate(): Promise<void> {
      const leadCandidate: ILeadCandidate = this.getLeadCandidateData()
      await leadsService.createLeadCandidate(leadCandidate)
    },
    getLeadCandidateData(): ILeadCandidate {
      const step: number = this.store.getters[`${SHOPPING}/getCurrentStep`]
      const steps: IStep[] = this.store.getters[`${SHOPPING}/getSteps`]
      const stepData: IStep | undefined = steps.find((st: IStep) => st.label === step.toString())
      const position: string =
        this.channel !== CHANNEL_WEB
          ? 'Confirmed'
          : stepData
          ? `${stepData.label} - ${stepData.name}`
          : `${step} - Final`
      const accountId: string = this.store.getters[`${SHOPPING}/${GET_ACCOUNT_ID}`]
      const newUser: IUser = this.store.getters[`${SHOPPING}/getNewUser`]
      const userDefaultContact = this.store.getters[`${SHOPPING}/getUserDefaultContact`]

      let lead: ILeadCandidate = {
        accountId,
        phone: userDefaultContact?.phone,
        email: userDefaultContact?.email,
        firstName: newUser.personalData.firstName,
        lastName1: newUser.personalData.lastName,
        fiscalIdNumber: newUser.personalData.documentNumber,
        birthdate: newUser.personalData.personBirthdate,
        position
      }

      return lead
    }
  }
})
</script>
