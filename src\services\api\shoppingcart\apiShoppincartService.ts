import axios from 'axios'

import { LANGUAGES, SELLS } from '../constants/api.constants'
import {
  X_PARLEM_API_KEY,
  CONTENT_TYPE,
  APPLICATION_JSON_PATCH_JSON,
  SHOPPINGCART,
  API,
  COMP<PERSON>IES,
  TRA<PERSON>MARKS,
  RATEGROUPS,
  FAMI<PERSON>IES,
  RATES,
  USERS,
  CARTS,
  OPPORTUNITIES,
  CUSTOMERS,
  DOCUMENT
} from '../../constants/services.constants'
import type { IApiRes, IHeaders } from '../interfaces'
import type { IApiShoppingcart } from './interfaces/api-shoppingcart.interface'
import type { ILeadSell } from './interfaces/lead-sell.interface'
import type { IRateDataParams } from './interfaces/rate-data-params.interface'
import type { IRate } from './interfaces/rate.interface'
import type { IfreezeCart } from '@/interfaces/freezeCart.interface'
import type { IUserInfo } from '@/services/users/interfaces/user-info.interface'
import { toastUtils } from '@/utils/toast'

const shoppingHeaders: IHeaders = {
  headers: {
    [X_PARLEM_API_KEY]: import.meta.env.VITE_API_KEY_SHOPPINGCART,
    [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON
  }
}

const apiShoppingcartService: IApiShoppingcart = {
  async getUserInfo(username: string) {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${SHOPPINGCART}/${API}/${USERS}/${username}`
    try {
      const response: IApiRes<IUserInfo> = await axios.get(url, shoppingHeaders as IHeaders)
      return response.data
    } catch (error: any) {
      return error.response
    }
  },

  async postLeadSell(lead: ILeadSell): Promise<IApiRes<ILeadSell>> {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${SELLS}`
    try {
      const response: IApiRes<ILeadSell> = await axios.post(url, lead, shoppingHeaders as IHeaders)
      return response
    } catch (error: any) {
      return error.response
    }
  },

  async getLeadSell(sellId: string): Promise<IApiRes<ILeadSell>> {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${SELLS}/${sellId}`
    try {
      const response: IApiRes<ILeadSell> = await axios.get(url, shoppingHeaders as IHeaders)
      return response
    } catch (error: any) {
      return error.response
    }
  },

  async getRateByCode(rateData: IRateDataParams): Promise<IRate> {
    try {
      const lang = rateData.lang || 'ca'
      const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${COMPANIES}/${
        rateData.company
      }/${TRADEMARKS}/${rateData.trademark}/${LANGUAGES}/${lang}/${RATES}/${rateData.code}`

      const response: IApiRes<IRate> = await axios.get(url, shoppingHeaders as IHeaders)
      return response.data
    } catch (error: any) {
      return error
    }
  },

  async getRateGroups(rateData: IRateDataParams): Promise<any> {
    try {
      const lang = rateData.lang || 'ca'
      const family = rateData.family || 'Mobile'
      const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${COMPANIES}/${
        rateData.company
      }/${TRADEMARKS}/${rateData.trademark}/${FAMILIES}/${family}/${LANGUAGES}/${lang}/${USERS}/${
        rateData.username
      }/${RATEGROUPS}`
      const response: IApiRes<any> = await axios.get(url, shoppingHeaders as IHeaders)
      return response.data
    } catch (error: any) {
      return error.response
    }
  },

  async getRateById(rateData: IRateDataParams): Promise<any> {
    try {
      const lang = rateData.lang || 'ca'
      const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${COMPANIES}/${
        rateData.company
      }/${TRADEMARKS}/${rateData.trademark}/${LANGUAGES}/${lang}/${RATEGROUPS}/${
        rateData.rateGroupId
      }/${USERS}/${rateData.username}/${RATES}`
      const response: IApiRes<any> = await axios.get(url, shoppingHeaders as IHeaders)
      return response.data
    } catch (error: any) {
      error
    }
  },

  async postFreezeCart(cart: IfreezeCart): Promise<IApiRes<IfreezeCart>> {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${CARTS}`
    try {
      const response: IApiRes<IfreezeCart> = await axios.post(
        url,
        cart,
        shoppingHeaders as IHeaders
      )
      return response
    } catch (error: any) {
      return error.response
    }
  },

  async putFreezeCart(cart: IfreezeCart): Promise<IApiRes<IfreezeCart>> {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${CARTS}/${
      cart.id
    }`
    try {
      const response: IApiRes<IfreezeCart> = await axios.put(url, cart, shoppingHeaders as IHeaders)
      return response
    } catch (error: any) {
      return error.response
    }
  },

  async getFreezeCart(cartId: string): Promise<IApiRes<IfreezeCart>> {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${CARTS}/${cartId}`
    try {
      const response: IApiRes<IfreezeCart> = await axios.get(url, shoppingHeaders as IHeaders)
      return response
    } catch (error: any) {
      toastUtils.showToast('error', "El carret congelat no s'ha pogut recuperar")
      return error.response
    }
  },

  async getFreezeCartWithOpportunityId(opportunityId: string): Promise<IApiRes<IfreezeCart>> {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${SHOPPINGCART}/${API}/${CARTS}/${OPPORTUNITIES}/${opportunityId}`
    try {
      const response: IApiRes<IfreezeCart> = await axios.get(url, shoppingHeaders as IHeaders)
      return response
    } catch (error: any) {
      toastUtils.showToast('error', "Aquesta oportunitat no s'ha pogut recuperar")
      return error.response
    }
  },

  async deleteFreezeCart(cartId: string): Promise<IApiRes<number>> {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPINGCART}/${API}/${CARTS}/${cartId}`
    try {
      const response: IApiRes<number> = await axios.delete(url, shoppingHeaders as IHeaders)
      return response
    } catch (error: any) {
      return error.response
    }
  },
  async getByUserId(company: string, documentId: string): Promise<any> {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${SHOPPINGCART}/${API}/${COMPANIES}/${company}/${CUSTOMERS}/${DOCUMENT}/${documentId}`

    const response: IApiRes<any> = await axios.get(url, shoppingHeaders as IHeaders)
    return response && response.data[0]
  }
}

export default apiShoppingcartService
