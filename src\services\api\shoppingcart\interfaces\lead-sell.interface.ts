import type { <PERSON><PERSON>Address } from './lead-address.interface'
import type { ILeadPersonalData } from './lead-personal-data.interface'
import type {ILeadContractedRates} from './lead-contracted-rates.interface'
import type { ILeadBillingInfo } from './lead-billing-info.interface'
import type { ILeadTerms } from './lead-terms.interface'

export interface ILeadSell {
  company: string,
  trademark: string,
  terms: ILeadTerms,
  soldby: string,
  soldAt: Date | string,
  customerId?: string,
  lead:{
    provisionContact: {
      name: string,
      email?: string
      phone?: string,
    },
    shippingAddress?: ILeadAddress,
    billingAddress?: ILeadAddress,
    personalData?: ILeadPersonalData,
    billingInfo?: ILeadBillingInfo
    gdprRegistry:ILeadTerms
    advertisingConfigurations?:ILeadTerms[]
  },
  contractedRates:ILeadContractedRates[]
}