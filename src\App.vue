<script lang="ts">
import { onMounted, ref } from 'vue'

export default {
  name: 'app-shopping',
  setup() {
    const configData = {
      lang: 'en',
      trademark: '<PERSON><PERSON><PERSON>',
      username: 'ccca<PERSON><PERSON>', //'<EMAIL>', //'nouBackEnd',//'<EMAIL>'//agent_aproop_1,
      rateCode: '', // 'RP-0820',
      productFamily: 'MobileFiberFix',
      gescal: '',
      opportunityId: '', //'65e09315173cf7950dd51910',
      userId: '37735951N', //'36904015D' //'38824676D' //'F02928471' // 15992391P
      cartId: ''
      /*  cartId: '661fcc8586472eb20e5d2149' */
    }
    const pwShopping: any = ref(null)
    let shopping: HTMLElement = document.createElement('pw-shopping')

    onMounted(() => {
      const configStr = JSON.stringify(configData)
      //let shopping: HTMLElement = document.createElement('pw-shopping')

      shopping.setAttribute('config', configStr)

      if (pwShopping.value) {
        pwShopping.value.appendChild(shopping)
      }
    })

    /*     function addCartId() {
      configData.cartId = '65dc4b1b607ddbb112f2f743'
      const configStr = JSON.stringify(configData)
      shopping.setAttribute('config', configStr)
    }
    function cleanCartId() {
      configData.cartId = ''
      const configStr = JSON.stringify(configData)
      shopping.setAttribute('config', configStr)
    } */

    return {
      pwShopping
      /*     cleanCartId,
      addCartId */
    }
  }
}
</script>

<template>
  <!--   <button type="button" @click="addCartId">Afegir Cart Id</button>
  <button type="button" @click="cleanCartId">Clean cart id</button>
 -->
  <div ref="pwShopping"></div>
</template>

<style scoped></style>
