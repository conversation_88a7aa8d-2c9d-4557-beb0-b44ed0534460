import type { IBillingData } from '../../../services/users/interfaces/billing-data.interface'
import type { IState } from './interfaces/state.interface'

export default (): IState => ({
  step: 2,
  steps: [],
  stepCompleted: 0,
  oldUser: null,
  newUser: null,
  billingData: {} as IBillingData,
  billingDataIsChanged: false,
  countries: [],
  streetTypes: [],
  pickLists: [],
  coverage: null,
  company: 'Parlem',
  userInfo: null,
  channel: '',
  trademark: '',
  username: 'nouBackEnd',
  lang: 'ca',
  rateCode: undefined,
  rateData: undefined,
  productFamily: 'Mobile',
  rate: null,
  catalog: [],
  bankAccountIsChanged: false,
  accountHolderIsChanged: false,
  accountId: '',
  userId: '',
  freezeCartId: null,
  userType: '',
  checkedSimAddress: false,
  shippingAddress: {},
  opportunityId: null,
  selectedDiscount: null,
  showNoAccountMessage: false,
  msalConfig: {
    auth: {
      clientId: 'f8a4c641-634e-4ef6-9b42-0211b8867662',
      authority: 'https://login.microsoftonline.com/81f874f1-9c91-4258-a004-842cfb2a92b7',
      redirectUri: '/auth'
    },
    cache: {
      cacheLocation: 'localStorage'
    }
  },
  accessToken: ''
  // checkedAuthorizedPerson: false,
  // authorizedPerson: {}
})
