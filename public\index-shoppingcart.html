<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/x-icon"
      href="https://parlem.com/wp-content/uploads/2020/04/cropped-favicon-parlem-1.png"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Shopping</title>
    <script src="#{cdn_base_url}#/shoppingcart/parlem-webcomponents-shopping.umd.js"></script>
  </head>
  <body>
    <div id="pw-shopping"></div>
  </body>
  <script>
    // Get params from url Add to url https://example.com/en/sells
    const queryString = window.location.search
    const urlParams = new URLSearchParams(queryString)
    const lang = urlParams.get('lang')
    const trademark = urlParams.get('trademark')
    const productFamily = urlParams.get('family')
    const rateCode = urlParams.get('rp')
    const userId = urlParams.get('userid')
    const username = urlParams.get('username')
    const cartId = urlParams.get('cartid')

    // Load webcomponent with dynamic params
    let shopping = document.createElement('pw-shopping')
    const config = {
      lang: lang ? lang : 'ca', // Possible values: 'ca' || 'es' || 'en'
      trademark: trademark ? trademark : 'Parlem',
      productFamily: productFamily ? productFamily : 'Mobile', // Possible values: 'MobileFiberFix' || 'FiberFix' || 'Mobile'
      rateCode: rateCode ? rateCode : '', // Specific rate code.
      username: username ? username : '', //'<EMAIL>', // Seller id. In web channel it is 'nouBackEnd'. Botiga channel: '<EMAIL>'
      userId: userId ? userId : '', // User identity document. userId example: '15992391P'
      cartId: cartId ? cartId : '' // Frozen cart id. cartId example: '64da0231c83bd82ef1f65be5'
    }
    const configStr = JSON.stringify(config)
    shopping.setAttribute('config', configStr)
    const pwShopping = document.getElementById('pw-shopping')
    pwShopping.appendChild(shopping)

    // Listener event
    window.addEventListener('submit-shopping-cart-event', (e) => {
      const response = e.detail
    })
  </script>
</html>
