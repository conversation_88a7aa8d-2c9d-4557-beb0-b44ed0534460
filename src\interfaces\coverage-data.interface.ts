export interface ICoverage {
  town?: string | null
  street?: string | null
  streetNumber?: string | null
  bis?: string | null
  gate?: string | null
  block?: string | null
  letter?: string | null
  stair?: string | null
  floor?: string | null
  firstHand?: string | null
  secondHand?: string | null
  fullAddress?: string | null
  gescal7?: string | null
  gescal12?: string | null
  gescal17?: string | null
  gescal37?: string | null
  country?: string | null
  state?: string | null
  streetType?: string | null
  ineCode?: string | null
  province?: string | null
  zip?: string | null
  from?: string
}
