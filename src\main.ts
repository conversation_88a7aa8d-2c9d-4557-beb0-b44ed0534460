import { createApp } from 'vue'
import App from './App.vue'

import { defineCustomElementWrapped } from '@/customElementWrapper'
import PwSelling from '@/webcomponents/PwSelling.ce.vue'
import store from '@/store'
import i18n from '@/i18n'
import '@/assets/styles/index.css'
import router from '@/router'

customElements.define('pw-shopping', defineCustomElementWrapped(PwSelling, [store, i18n, router]))

createApp(App).use(store).use(i18n).mount('#app-shopping')
