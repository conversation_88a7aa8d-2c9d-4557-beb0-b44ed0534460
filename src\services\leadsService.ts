import axios from 'axios'
import type { CancelTokenSource } from 'axios'
import type { ILeadCandidate } from '@/interfaces/lead-candidate'
import store from '@/store'
import { SET_ACCOUNT_ID, SHOPPING } from '@/store/modules/shopping/constants/store.constants'

let source: CancelTokenSource = axios.CancelToken.source()
let pendingCall: boolean = false

const leadsService = {
    post(lead: object) {
        pendingCall = true
        // TODO: change the endpoint
        return axios.get(`${ import.meta.env.VITE_BASE_URL }/coverage/api/picklists/territoryowners`,
            {cancelToken: source.token, headers: {'x-parlemapikey': import.meta.env.VITE_API_KEY_COVERAGE, 'content-type': 'application/json-patch+json'}}
        )
        .then((response: any) => {
            pendingCall = false
            return response && response.data
        })
    },
    async postAsync(lead: object) {
        try {
            const leadApiResponse = await this.post(lead)
            pendingCall = false

            return leadApiResponse
        } catch (error) {
            pendingCall = false

            return error
        }
    },
    cancel(cancelMessage: string) {
        source.cancel(cancelMessage)
        pendingCall = false
        this.refreshToken()
        return this
    },
    refreshToken(): void {
        source = axios.CancelToken.source()
    },
    isPendingCall(): boolean {
        return pendingCall
    },
    createLeadCandidate(lead: ILeadCandidate): Promise<ILeadCandidate> {
        pendingCall = true

        return axios.post(`${ import.meta.env.VITE_BASE_URL }/lead/api/candidate`,
          lead,
          {cancelToken: source.token, headers: {'x-parlemapikey': import.meta.env.VITE_API_KEY_LEAD, 'content-type': 'application/json-patch+json'}}
        )
        .then((response: any) => {
            pendingCall = false
            if (response?.data?.id) {
                store.dispatch(`${ SHOPPING }/${ SET_ACCOUNT_ID }`, response.data.id)
            }

            return response && (response.data as ILeadCandidate)
        })
    }
}

export default leadsService