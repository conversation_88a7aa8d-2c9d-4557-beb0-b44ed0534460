<template id="streetmap">
  <pw-street-map id="streetmap" :config="configStr" :data="dataStr"></pw-street-map>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch, computed, type Ref } from 'vue'
import en from '@/i18n/locales/en.json'
import es from '@/i18n/locales/es.json'
import ca from '@/i18n/locales/ca.json'
import gl from '@/i18n/locales/gl.json'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { SHOPPING } from '@/store/modules/shopping/constants/store.constants'
import type { ICoverage } from '@/interfaces/coverage-data.interface'

export default defineComponent({
  name: 'PwStreetMap',
  emits: ['saveAddress'],
  props: {
    address: {
      type: Object
    },
    from: {
      type: String,
      default: ''
    }
  },

  setup(props: any, { emit }: any) {
    const pwStreetMap: any = ref(null)

    let recoveryStreetMapData: any = ref({})
    const store = useStore()
    const lang: Ref<string> = computed(() => store.getters[`${SHOPPING}/getLang`])
    const { t, locale }: any = useI18n({
      messages: {
        en,
        gl,
        es,
        ca
      }
    })
    locale.value = lang.value || locale.value
    const conf: any = {
      lang: lang.value,
      back: 'false',
      from: props.from
    }
    onMounted((): void => {
      loadCoverageComponent()
    })

    if (props.address && (props.address.gescal17 || props.address.gescal37)) {
      recoveryStreetMapData.value = recoveryStreetMapAddress(props.address)
    }

    const configStr = JSON.stringify(conf)
    const dataStr = JSON.stringify(recoveryStreetMapData.value)

    function loadCoverageComponent(): void {
      if (props.address && (props.address.gescal17 || props.address.gescal37)) {
        recoveryStreetMapData.value = recoveryStreetMapAddress(props.address)
      }
      const existScript: HTMLElement | null = document.getElementById('wc-coverage')
      if (!existScript) {
        const coverageScript: HTMLScriptElement = document.createElement('script')
        coverageScript.setAttribute('src', import.meta.env.VITE_COVERAGE_WC)
        coverageScript.setAttribute('id', 'wc-coverage')
        coverageScript.async = true
        document.head.appendChild(coverageScript)
      }
      let streetmap = document.createElement('pw-street-map')
      const conf: any = {
        lang: lang.value,
        back: 'true'
      }
      const configStr = JSON.stringify(conf)
      streetmap.setAttribute('config', configStr)

      const recoveryCoverageDataStr = JSON.stringify(recoveryStreetMapData.value || '{}')

      if (recoveryCoverageDataStr !== '{}') {
        streetmap.setAttribute('data', recoveryCoverageDataStr)
      }

      if (pwStreetMap.value) {
        pwStreetMap.value.appendChild(streetmap)
      }
    }

    function recoveryStreetMapAddress(address: ICoverage) {
      return {
        town: address.town,
        ineCode: address.ineCode,
        street: address.street,
        streetNumber: address.streetNumber,
        block: address.block,
        bis: address.bis,
        gate: address.gate,
        letter: address.letter,
        stair: address.stair,
        floor: address.floor,
        firstHand: address.firstHand,
        secondHand: address.secondHand,
        fullAddress: address.fullAddress,
        gescal7: address.gescal7,
        gescal12: address.gescal12,
        gescal17: address.gescal17,
        gescal37: address.gescal37,
        zip: address.zip
      }
    }

    window.addEventListener('submit-coverage-event', (e: any) => {
      const address: ICoverage = e.detail as ICoverage
      emit('saveAddress', address)
    })

    return {
      t,
      recoveryStreetMapData,
      configStr,
      dataStr
    }
  }
})
</script>
