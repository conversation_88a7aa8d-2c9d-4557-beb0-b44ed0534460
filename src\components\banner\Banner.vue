<template>
  <PwBanner
    v-if="bannerOptions?.length"
    :lang="locale"
    :options="bannerOptions"
    style="background: #f6f3f3; padding-bottom: 8px"
  />
</template>

<script lang="ts">
import { PwBanner } from 'parlem-webcomponents-common'
import { defineComponent } from 'vue'
import { useStore } from 'vuex'
import en from '@/i18n/locales/en.json'
import es from '@/i18n/locales/es.json'
import ca from '@/i18n/locales/ca.json'
import gl from '@/i18n/locales/gl.json'
import { useI18n } from 'vue-i18n'
import type { IRate } from '@/services/api/shoppingcart/interfaces/rate.interface'
import { SHOPPING } from '@/store/modules/shopping/constants/store.constants'

export default defineComponent({
  name: 'Pw-Banner',
  props: {
    lang: String
  },
  components: {
    PwBanner
  },
  computed: {
    rate(): IRate {
      return this.store.getters[`${SHOPPING}/getRate`]
    },
    bannerOptions() {
      const packProducts: any = this.rate?.pack?.products
      const options = []

      if (packProducts.some((product: any) => product.specifications.fibSpeed)) {
        const product = packProducts.find((product: any) => product.specifications.fibSpeed)
        options.push({
          title: this.t('banner.fixFiber'),
          value: `${product.specifications.fibSpeed}MB`,
          titleStyles: '',
          valueStyles: 'font-size: 1.875rem;',
          isPrice: false,
          isPromotion: false
        })
      }

      if (packProducts.some((product: any) => product.specifications.mbData)) {
        const product = packProducts.find((product: any) => product.specifications.mbData)
        if (product.specifications.mbData.includes('il·limitades')) {
          options.push({
            title: this.t('banner.mobile'),
            value: 'il·limitat*',
            titleStyles: '',
            valueStyles: 'font-size: 1.875rem;',
            isPrice: false,
            isPromotion: false
          })
        } else {
          options.push({
            title: this.t('banner.mobile'),
            value: product.specifications.mbData,
            titleStyles: '',
            valueStyles: 'font-size: 1.875rem;',
            isPrice: false,
            isPromotion: false
          })
        }
      }

      if (this.rate && this.rate.display) {
        let details: any = {
          title: this.t('banner.price'),
          value: this.rate?.offerPrice.priceWithVATFinal || this.rate?.finalPrice.priceWithVATFinal,
          titleStyles: '',
          valueStyles: '',
          isPrice: true,
          isPromotion: false
        }
        if (
          this.rate.offerPrice?.priceWithVATFinal &&
          this.rate.offerPrice?.durationDaysPromotionalPrize
        ) {
          details = { ...details, isPromotion: this.rate.offerPrice.durationDaysPromotionalPrize }
        }

        options.push(details)
      }
      return options
    }
  },
  setup(props: any) {
    const store = useStore()
    const { t, locale }: any = useI18n({
      messages: {
        en,
        gl,
        es,
        ca
      }
    })
    locale.value = props.lang || locale.value
    return {
      t,
      store,
      locale
    }
  }
})
</script>

<style></style>
