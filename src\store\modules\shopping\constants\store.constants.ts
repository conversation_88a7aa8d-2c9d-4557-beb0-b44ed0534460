import type { IStep } from '@/interfaces/step.interface'

export const NEXT_STEP: string = 'nextStep'
export const PREV_STEP: string = 'prevStep'
export const STEP_COMPLETED: string = 'stepCompleted'
export const SET_OLD_USER: string = 'setOldUser'
export const SET_NEW_USER: string = 'setNewUser'
export const SET_ACCESS_TOKEN: string = 'setAccessToken'
export const SET_ACCOUNT: string = 'setAccount'
export const GET_MSAL_CONFIG: string = 'getMsalConfig'
export const SET_COMPANY: string = 'setCompany'
export const GET_COMPANY: string = 'getCompany'
export const SET_CHANNEL: string = 'setChannel'
export const GET_CHANNEL: string = 'getChannel'
export const SET_TRADEMARK: string = 'setTrademark'
export const GET_TRADEMARK: string = 'getTrademark'
export const SET_USERNAME: string = 'setUsername'
export const GET_USERNAME: string = 'getUsername'
export const GET_DISCOUNTS: string = 'getDiscounts'
export const SET_RATE_CODE: string = 'setRateCode'
export const GET_RATE_CODE: string = 'getRateCode'
export const SET_RATE: string = 'setRate'
export const GET_RATE: string = 'getRate'
export const GET_RATE_BY_CODE: string = 'getRateByCode'
export const GET_RATE_GROUPS: string = 'getRateGroups'
// export const  SET_RATE_DATA: string = 'setRateData'
export const SET_PRODUCT_FAMILY: string = 'setProductFamily'
export const SET_LANG: string = 'setLang'
// export const  SET_FAMILY: string = 'setFamily'
export const REMOVE_NEW_USER: string = 'removeNewUser'
export const REMOVE_OLD_USER: string = 'oldNewUser'
export const UPDATE_NEW_USER: string = 'updateNewUser'
export const SET_GDPR: string = 'setGdpr'
export const SET_ADV: string = 'setAdv'
export const GET_COUNTRIES: string = 'getCountries'
export const SET_COUNTRIES: string = 'setCountries'
export const GET_PICKLISTS: string = 'getPickLists'
export const SET_PICKLISTS: string = 'setPickLists'
export const SET_LEAD_SELL: string = 'setLeadSell'
export const GET_LEAD_SELL: string = 'getLeadSell'
export const SET_FREEZE_CART: string = 'setFreezeCart'
export const SAVE_FREEZE_CART: string = 'saveFreezeCart'
export const GET_FREEZE_CART: string = 'getFreezeCart'
export const GET_OPPORTUNITY_ID: string = 'getOpportunityId'
export const SAVE_OPPORTUNITY_ID: string = 'saveOpportunityId'
export const GET_FREEZE_CART_WITH_OPPORTUNITY_ID: string = 'getFreezeCartWithOpportunityId'
export const UPDATE_FREEZE_CART: string = 'updateFreezeCart'
export const DELETE_FREEZE_CART: string = 'deleteFreezeCart'
export const RECOVER_SELLING_DATA: string = 'recoverSellingData'
export const SET_STREET_TYPES: string = 'setStreetTypes'
export const GPDR_PRIVACY_POLICY_ID = 'gpdr_privacy_policy'
export const GPDR_COMMERCIAL_COMMUNICATIONS_ID = 'Comercial'
export const GPDR_NON_COMMERCIAL_COMMUNICATIONS_ID = 'NoComercial'
export const GPDR_THIRD_PARTY_COMMERCIAL_COMMUNICATIONS_ID = '3PartyComercial'
export const SET_BILLING_DATA: string = 'setBillingData'
export const SET_BILLING_ADDRESS: string = 'setBillingAddress'
export const GET_BILLING_DATA: string = 'getBillingData'
export const SET_SIM_ADDRESS: string = 'setSimAddress'
export const REMOVE_SIM_ADDRESS: string = 'removeSimAddress'
export const GET_SIM_ADDRESS: string = 'getSimAddress'
export const SHOPPING: string = 'shopping'
export const SET_COVERAGE: string = 'setCoverage'
export const SET_ACCOUNT_HOLDER: string = 'setAccountHolder'
export const SET_BANK_ACCOUNT: string = 'setBankAccount'
export const SET_STEP: string = 'setStep'
export const SET_STEPS: string = 'setSteps'
export const SET_CATALOG: string = 'setCatalog'
export const SET_ACCOUNT_ID: string = 'setAccountId'
export const SET_USER_ID: string = 'setUserId'
export const GET_USER_ID: string = 'getUserId'
export const SET_USER_TYPE: string = 'setUserType'
export const GET_USER_TYPE: string = 'getUserType'
export const GET_ACCOUNT_ID: string = 'getAccountId'
export const GET_ACCOUNT_USERNAME: string = 'getAccountUsername'
export const SET_CHECKED_SIM_ADDRESS: string = 'setCheckedSimAddress'
// export const SET_CHECKED_AUTHORIZE_PERSON: string = 'setCheckedAuthorizePerson'
// export const SET_AUTHORIZED_PERSON: string = 'setAuthorizedPerson'
export const SET_GESCAL: string = 'setGescal'
export const GET_GESCAL: string = 'getGescal'
export const GET_STEPS: string = 'getSteps'
export const GET_USER_INFO: string = 'getUserInfo'
export const SET_USER_INFO: string = 'setUserInfo'
export const CLEAN_SELLING_DATA: string = 'cleanSellingData'
export const SET_SELECTED_DISCOUNT: string = 'setSelectedDiscount'
export const GET_SELECTED_DISCOUNT: string = 'getSelectedDiscount'
export const SET_SHOW_NO_ACCOUNT_MESSAGE: string = 'setShowNoAccountMessage'
export const GET_SHOW_NO_ACCOUNT_MESSAGE: string = 'getShowNoAccountMessage'

export const INITIAL_STEPS: IStep[] = [
  {
    label: '1',
    description: 'stepper.products',
    route: 'products',
    name: 'products'
  },
  {
    label: '2',
    description: 'stepper.personal-data',
    route: 'personal-data',
    name: 'personal-data'
  },
  {
    label: '3',
    description: 'stepper.billing-data',
    route: 'billing-data',
    name: 'billing-data'
  },
  {
    label: '4',
    description: 'stepper.confirmation',
    route: 'resum-data',
    name: 'resum-data'
  }
]

export const WEB_CHANNEL_STEPS: IStep[] = [
  {
    label: '1',
    description: 'stepper.personal-data',
    route: 'personal-data',
    name: 'personal-data'
  },
  {
    label: '2',
    description: 'stepper.products',
    route: 'products',
    name: 'products'
  },
  {
    label: '3',
    description: 'stepper.billing-data',
    route: 'billing-data',
    name: 'billing-data'
  },
  {
    label: '4',
    description: 'stepper.confirmation',
    route: 'resum-data',
    name: 'resum-data'
  }
]
