import type { ICoverage } from '@/interfaces/coverage-data.interface'
import type { IPickList, IPickListValue } from '@/services/api/crm/interfaces'
import type { IUser } from '@/services/users/interfaces'
import type { IBillingData } from '@/services/users/interfaces/billing-data.interface'
import type { IStep } from '@/interfaces/step.interface'
import type { ICatalog } from '@/interfaces/catalog.interface'
import type { IRate } from '@/services/api/shoppingcart/interfaces/rate.interface'
import type { IAddressForm } from '@/components/billing-address/interfaces/address-form.interface'
import type { IUserInfo } from '@/services/users/interfaces/user-info.interface'

export interface IState {
  step: number
  steps: IStep[]
  stepCompleted: number
  oldUser: IUser | null
  newUser: IUser | null
  billingData: IBillingData
  billingDataIsChanged: boolean
  countries: IPickListValue[]
  streetTypes: IPickListValue[]
  pickLists: IPickList[]
  coverage: ICoverage | null
  userInfo: IUserInfo | null
  company: string
  channel: string
  trademark: string
  username: string
  lang: string
  rateCode?: string | undefined
  rateData?: object | undefined
  productFamily?: string | undefined
  rate: IRate | null
  catalog: ICatalog[]
  bankAccountIsChanged: boolean
  accountHolderIsChanged: boolean
  accountId: string
  userId: string
  gescal?: string | undefined
  freezeCartId: string | null
  userType?: string
  checkedSimAddress: boolean
  shippingAddress: IAddressForm
  opportunityId: string | null
  selectedDiscount: IPickListValue | null
  showNoAccountMessage: boolean
  msalConfig: {
    auth: {
      clientId: string
      authority: string
      redirectUri: string
    }
    cache: {
      cacheLocation: string
    }
  }
  accessToken: string
  // checkedAuthorizedPerson: boolean
  // authorizedPerson: object
}
