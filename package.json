{"name": "parlem-webcomponents-shopping", "version": "2.4.70", "description": "PWC Shopping", "homepage": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-shopping", "main": "./dist/parlem-webcomponents-shopping.umd.js", "module": "./dist/parlem-webcomponents-shopping.es.js", "exports": {".": {"import": "./dist/parlem-webcomponents-shopping.es.js", "require": "./dist/parlem-webcomponents-shopping.umd.js"}}, "files": ["dist/*"], "scripts": {"dev": "vite --force true", "staging": "vite --mode staging --force true", "build": "run-p type-check build:prod", "preview": "vite preview", "test:unit": "vitest", "build:dev": "vite build --mode dev", "build:staging": "vite build --mode staging", "build:prod": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"url": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-shopping", "type": "git"}, "bugs": {"url": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-shopping", "email": "<EMAIL>"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@intlify/unplugin-vue-i18n": "^0.10.0", "@tailwindcss/container-queries": "^0.1.1", "@vuelidate/core": "^2.0.2", "@vuelidate/validators": "^2.0.2", "axios": "^1.3.5", "install": "^0.13.0", "moment": "^2.29.4", "parlem-webcomponents-common": "1.1.220", "postcss-nesting": "^11.2.2", "vue": "^3.4.21", "vue-i18n": "^9.2.2", "vue-router": "4", "vue-toast-notification": "^3.1.2", "vuex": "^4.0.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@types/jsdom": "^21.1.0", "@types/node": "^18.19.31", "@vitejs/plugin-vue": "^4.1.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/test-utils": "^2.3.0", "@vue/tsconfig": "^0.1.3", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "jsdom": "^21.1.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.38", "prettier": "^2.8.4", "tailwindcss": "^3.4.3", "typescript": "~5.4.5", "vite": "^4.1.4", "vitest": "^0.29.1", "vue-tsc": "^1.8.27"}, "peerDependencies": {"postcss": "^8.4.38"}}