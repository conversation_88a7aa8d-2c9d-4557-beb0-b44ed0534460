export interface IInfoUser {
  id?: string;
  advertisingConfigurations?: Array<{
    company?: string;
    messageType?: string;
    accepted?: boolean;
    acceptanceDate?: string;
    declinationDate?: string;
  }>;
  gdprRegistries?: Array<{
    company?: string;
    accepted?: boolean;
    acceptanceDate?: string;
  }>;
  shippingAddresses?: Array<{
    country?: string;
    province?: string;
    city?: string;
    streetType?: string;
    street?: string;
    number?: string;
    specification?: string;
    zip?: string;
    isDefault?: boolean;
  }>;
  billingAddresses?: Array<{
    country?: string;
    province?: string;
    city?: string;
    streetType?: string;
    street?: string;
    number?: string;
    specification?: string;
    zip?: string;
    isDefault?: boolean;
  }>;
  provisionContacts?: Array<{
    name?: string;
    email?: string;
    phone?: string;
    isDefault?: boolean;
  }>;
  personalData?: {
    gender?: string;
    firstName?: string;
    lastName?: string;
    documentType?: string;
    documentNumber?: string;
    nationality?: string;
    completeName?: string;
    personBirthdate?: string;
    customerType?: string;
  };
  companyStructure?: Record<string, any>;
  billingInfos?: Array<{
    cccOwner?: string;
    cccOwnerIdentification?: string;
    iban?: string;
    sendBill?: string;
    isDefault?: boolean;
  }>;
  authorizedAccounts?: Array<Record<string, any>>;
  externalSystemInfos?: Array<{
    externalSystem?: string;
    externalId?: string;
    customerId?: string;
  }>;
}
