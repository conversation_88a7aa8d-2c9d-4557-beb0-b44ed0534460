import type { <PERSON>ick<PERSON>istValue, IPickList } from '@/services/api/crm/interfaces'
import type { IAdvertisingConfig, IGdprRegistry, IUser } from '@/services/users/interfaces'
import type { IBillingData } from '@/services/users/interfaces/billing-data.interface'
import type { IState } from '@/store/modules/shopping/interfaces/state.interface'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import type { IStep } from '@/interfaces/step.interface'
import type { ICatalog } from '@/interfaces/catalog.interface'
import type { IRate } from '@/services/api/shoppingcart/interfaces/rate.interface'
import type { IAddressForm } from '../../../components/billing-address/interfaces/address-form.interface'
import type { IUserInfo } from '@/services/users/interfaces/user-info.interface'

export const nextStep = (state: IState): void => {
  if (state.step < state.steps?.length + 1) {
    state.step++
  }
}

export const stepCompleted = (state: IState, step: number): void => {
  if (step <= state.steps?.length + 1) {
    state.stepCompleted = step
  }
}

export const setAccessToken = (state: IState, accessToken: string): void => {
  state.accessToken = accessToken
}

export const prevStep = (state: IState): void => {
  if (state.step >= 2) {
    state.step--
  }
}

export const setStep = (state: IState, step: number): void => {
  state.step = step
}

export const setSteps = (state: IState, steps: IStep[]): void => {
  state.steps = [...steps]
}

export const setOldUser = (state: IState, user: IUser): void => {
  state.oldUser = user
}

export const removeOldUser = (state: IState, user: Partial<IUser>): void => {
  state.oldUser = null
}

export const updateNewUser = (state: IState, user: Partial<IUser>): void => {
  state.newUser = user as IUser
}

export const setNewUser = (state: IState, user: IUser): void => {
  state.newUser = user
}

export const setAccountId = (state: IState, accountId: string): void => {
  state.accountId = accountId
}

export const setCheckedSimAddress = (state: IState, checkedSimAddress: boolean): void => {
  state.checkedSimAddress = checkedSimAddress
}

// export const setCheckedAuthorizePerson = (state: IState, checkedAuthorizePerson: boolean): void => {
//   state.checkedAuthorizedPerson = checkedAuthorizePerson
// }

// export const setAuthorizedPerson = (
//   state: IState,
//   authorizedPerson: { name: string; phone: string }
// ): void => {
//   state.authorizedPerson = authorizedPerson
// }

export const setUserId = (state: IState, userId: string): void => {
  state.accountId = userId
}

export const setGdpr = (state: IState, gdpr: IGdprRegistry[]): void => {
  if (state.newUser && gdpr?.length) {
    state.newUser.gdprRegistries = [...gdpr]
  }
}

export const setAdv = (state: IState, adv: IAdvertisingConfig[]): void => {
  if (state.newUser && adv?.length) {
    state.newUser.advertisingConfigurations = [...adv]
  }
}

export const removeNewUser = (state: IState, user: Partial<IUser>): void => {
  state.newUser = null
}

export const setCountries = (state: IState, countries: IPickListValue[]): void => {
  state.countries = countries
}

export const setUserType = (state: IState, userType: string): void => {
  state.userType = userType
}

export const setStreetTypes = (state: IState, streetTypes: IPickListValue[]): void => {
  state.streetTypes = streetTypes
}

export const setPickLists = (state: IState, pickLists: IPickList[]): void => {
  state.pickLists = pickLists
}

export const setBillingData = (
  state: IState,
  billingData: { billingData: IBillingData; isChanged: boolean }
): void => {
  state.billingData = { ...state.billingData, ...billingData.billingData }
  state.billingDataIsChanged = billingData.isChanged
}

export const setBillingAddress = (
  state: IState,
  billingData: { billingAddress: IAddressForm; isChanged: boolean }
): void => {
  state.billingData.billingAddress = { ...billingData.billingAddress }
  state.billingDataIsChanged = billingData.isChanged
}
export const setSimAddress = (state: IState, address: IAddressForm): void => {
  state.shippingAddress = { ...address }
}

export const removeSimAddress = (state: IState): void => {
  state.shippingAddress = {} as IAddressForm
}

export const setCoverage = (state: IState, coverage: ICoverage): void => {
  state.coverage = coverage
}

export const setBankAccount = (state: IState, bankAccount: string): void => {
  if (state?.billingData) {
    state.billingData.iban = bankAccount
    state.bankAccountIsChanged = true
  }
}

export const setAccountHolder = (
  state: IState,
  accountHolder: { cccOwner: string; cccOwnerIdentification: string }
): void => {
  if (state?.billingData) {
    state.billingData = {
      ...state.billingData,
      cccOwner: accountHolder.cccOwner,
      cccOwnerIdentification: accountHolder.cccOwnerIdentification
    }
    state.accountHolderIsChanged = true
  }
}

export const setCompany = (state: IState, company: string): void => {
  state.company = company
}

export const setChannel = (state: IState, channel: string): void => {
  state.channel = channel
}

export const setTrademark = (state: IState, trademark: string): void => {
  state.trademark = trademark
}

export const setShowNoAccountMessage = (state: IState, showNoAccountMessage: boolean): void => {
  state.showNoAccountMessage = showNoAccountMessage
}

export const setUsername = (state: IState, username: string): void => {
  state.username = username
}

export const setSelectedDiscount = (state: IState, selectedDiscount: IPickListValue): void => {
  state.selectedDiscount = selectedDiscount
}

export const setUserInfo = (state: IState, userInfo: IUserInfo): void => {
  state.userInfo = userInfo
}

export const setCatalog = (state: IState, catalog: ICatalog[]): void => {
  state.catalog = catalog
}

export const setLang = (state: IState, lang: string): void => {
  state.lang = lang
}

export const setProductFamily = (state: IState, productFamily: string | undefined): void => {
  state.productFamily = productFamily
}

export const setRateCode = (state: IState, rateCode: string | undefined): void => {
  state.rateCode = rateCode
}

export const setRate = (state: IState, rate: IRate): void => {
  state.rate = rate
}

export const setGescal = (state: IState, gescal?: string | undefined): void => {
  state.gescal = gescal
}

export const setFreezeCart = (state: IState, freezeCartId: string | null): void => {
  state.freezeCartId = freezeCartId
}

export const saveOpportunityId = (state: IState, opportunityId: string): void => {
  state.opportunityId = opportunityId
}

export const cleanSellingData = (state: IState): void => {
  state.step = 1
  state.steps = []
  state.stepCompleted = 0
  state.oldUser = null
  state.newUser = null
  state.billingData = {} as IBillingData
  state.billingDataIsChanged = false
  state.countries = []
  state.streetTypes = []
  state.pickLists = []
  state.coverage = null
  state.company = 'Parlem'
  state.channel = ''
  state.trademark = ''
  state.username = 'nouBackEnd'
  state.lang = 'ca'
  state.rateCode = undefined
  state.rateData = undefined
  state.productFamily = 'Mobile'
  state.rate = null
  state.catalog = []
  state.bankAccountIsChanged = false
  state.accountHolderIsChanged = false
  state.accountId = ''
  state.userId = ''
  state.freezeCartId = null
  state.userType = ''
  state.checkedSimAddress = false
  state.shippingAddress = {}
  state.opportunityId = null
}
