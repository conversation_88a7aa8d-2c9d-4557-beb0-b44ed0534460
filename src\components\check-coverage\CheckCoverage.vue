<template id="coverage">
  <PwSeparator :title="t('coverage_form.check_coverage')"></PwSeparator>

  <p class="p-4">
    {{ t('coverage.description') }}
  </p>
  <div class="p-4" ref="pwCoverage"></div>
</template>

<script lang="ts">
import en from '@/i18n/locales/en.json'
import es from '@/i18n/locales/es.json'
import ca from '@/i18n/locales/ca.json'
import gl from '@/i18n/locales/gl.json'

import { useI18n } from 'vue-i18n'
import { PwSeparator } from 'parlem-webcomponents-common'
import { ref, onMounted, computed, type Ref } from 'vue'
import { useStore } from 'vuex'
import type { IStep } from '@/interfaces/step.interface'
import {
  GET_ACCOUNT_ID,
  GET_TRADEMARK,
  SET_COVERAGE,
  SET_STEP,
  SHOPPING
} from '@/store/modules/shopping/constants/store.constants'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import { leadsService, stepperService } from '@/services'
import type { ILeadCandidate } from '@/interfaces/lead-candidate'
import type { IUser } from '@/services/users/interfaces'

export default {
  name: 'check-coverage',
  components: {
    PwSeparator
  },
  setup() {
    const pwCoverage: any = ref(null)
    const store = useStore()
    const lang: Ref<string> = computed(() => store.getters[`${SHOPPING}/getLang`])
    let stepedNext: boolean = false

    const { t, locale }: any = useI18n({
      messages: {
        en,
        gl,
        es,
        ca
      }
    })
    locale.value = lang.value || locale.value
    const steps: Ref<IStep[]> = computed(() => store.getters[`${SHOPPING}/getSteps`])
    let coverageSend: boolean = false

    onMounted((): void => {
      const curr: number = store.getters[`${SHOPPING}/getCurrentStep`]
      const nextStep = steps.value.find((step: IStep) => step.label === curr.toString())
      if (nextStep?.label) {
        store.dispatch(`${SHOPPING}/${SET_STEP}`, parseInt(nextStep.label))
      }

      loadCoverageComponent()
    })

    function loadCoverageComponent(): void {
      const recoveryCoverageData = store.getters[`${SHOPPING}/getCoverage`]
      const existScript: HTMLElement | null = document.getElementById('wc-coverage')
      if (!existScript) {
        const coverageScript: HTMLScriptElement = document.createElement('script')
        coverageScript.setAttribute('src', import.meta.env.VITE_COVERAGE_WC)
        coverageScript.setAttribute('id', 'wc-coverage')
        coverageScript.async = true
        document.head.appendChild(coverageScript)
      }
      let coverage = document.createElement('pw-user-address-coverage-form')
      const trademark: Ref<IUser> = computed(() => store.getters[`${SHOPPING}/${GET_TRADEMARK}`])

      const conf: any = {
        lang: lang.value,
        back: 'true',
        trademark
      }
      const configStr = JSON.stringify(conf)
      coverage.setAttribute('config', configStr)

      const recoveryCoverageDataStr = JSON.stringify(recoveryCoverageData || '{}')

      if (recoveryCoverageDataStr !== '{}') {
        coverage.setAttribute('data', recoveryCoverageDataStr)
      }

      if (pwCoverage.value) {
        pwCoverage.value.appendChild(coverage)
      }
    }

    window.addEventListener('submit-coverage-event', (e: any) => {
      const coverage: ICoverage = e.detail as ICoverage
      if (!stepedNext && !coverageSend && coverage) {
        coverageSend = true
        stepedNext = true
        store.dispatch(`${SHOPPING}/${SET_COVERAGE}`, coverage)
        createLeadCandidate()
        const currentStep: number = store.getters[`${SHOPPING}/getCurrentStep`]
        setCompletedStep(currentStep)
        nextStep()
      }
    })

    window.addEventListener('back-coverage-event', (e: any) => {
      const event: { action: string } = e.detail as { action: string }
      if (event?.action && event.action === 'back') {
        prevStep()
      }
    })

    function nextStep(): void {
      stepperService.nextStep()
    }

    function prevStep(): void {
      stepperService.prevStep()
    }

    function setCompletedStep(currentStep: number): void {
      stepperService.setCompletedStep(currentStep)
    }

    async function createLeadCandidate(): Promise<void> {
      const leadCandidate: ILeadCandidate = getLeadCandidateData()
      const newLeadCandidate: ILeadCandidate = await leadsService.createLeadCandidate(leadCandidate)
    }

    function getLeadCandidateData(): ILeadCandidate {
      const newUser: IUser = store.getters[`${SHOPPING}/getNewUser`]
      const userDefaultContact = store.getters[`${SHOPPING}/getUserDefaultContact`]
      const step: number = store.getters[`${SHOPPING}/getCurrentStep`]
      const steps: IStep[] = store.getters[`${SHOPPING}/getSteps`]
      const stepData: IStep | undefined = steps.find((st: IStep) => st.label === step.toString())
      const position: string = stepData ? `${stepData.label} - ${stepData.name}` : `${step}`
      const accountId: string = store.getters[`${SHOPPING}/${GET_ACCOUNT_ID}`]

      let lead: ILeadCandidate = {
        accountId,
        phone: userDefaultContact?.phone,
        email: userDefaultContact?.email,
        firstName: newUser?.personalData?.firstName,
        lastName1: newUser?.personalData?.lastName,
        fiscalIdNumber: newUser?.personalData?.documentNumber,
        birthdate: newUser?.personalData?.personBirthdate,
        position
      }

      return lead
    }

    return {
      t,
      pwCoverage
    }
  }
}
</script>
