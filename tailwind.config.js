/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./public/**/*.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'media', // or 'media' or 'class'
  mode: 'jit',
  theme: {
    extend: {},
    colors: {
      primary: 'rgb(var(--color-primary) / <alpha-value>)',
      secondary: 'rgb(var(--color-secondary) / <alpha-value>)',
      'primary-light': 'rgb(var(--color-primary-light) / <alpha-value>)',
      'gray-light': '#E1E1E1',
      white: '#ffffff',
      black: '#000000',
      error: '#9B1C1C',
      warning: '#C27803',
      success: '#0E9F6E',
      info: '#6B7280',
      gray: '#c1c1c1',
      'slate-200': '#e2e8f0',
      dark: '#181818',
      'dark-gray-light': '#5B5F62',
      'dark-gray': '#3c4043'
    }
  },
  variants: {
    extend: {}
  },
  plugins: [require('@tailwindcss/container-queries')]
}
