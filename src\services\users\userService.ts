import axios from 'axios'
import type { <PERSON>A<PERSON>, IUser } from './interfaces'
import {
    API,
    APPLICATION_JSON_PATCH_JSON,
    AUTH,
    COMPANIES,
    CONTENT_TYPE,
    CUSTOMERS,
    CUSTOMER_AREA,
    SHOPPINGCART,
    TOKEN,
    X_PARLEM_API_KEY,
    CRM
} from '../constants/services.constants'
import type { IHeaders, IApiRes } from '../api/interfaces'


const usersService = {
    async login(documentNumber: string, password: string) {
        const url: string = `${ import.meta.env.VITE_BASE_URL }/${ CUSTOMER_AREA }/${ API }/${ AUTH }/${ TOKEN}`
        const customerAreaHeaders: IHeaders = {
            headers: {
                [X_PARLEM_API_KEY]: import.meta.env.VITE_API_KEY_CUSTOMER_AREA,
                [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON
            }
        }
        const response: IApiRes<IAuth> = await axios.post(url, {
            documentNumber,
            password

        }, customerAreaHeaders)

        return response && response.data
    },
    async get(id: string, company: string) {
        const url: string = `${ import.meta.env.VITE_BASE_URL }/${ SHOPPINGCART }/${ API }/${ COMPANIES }/${ company }/${ CUSTOMERS }/${ id }`
        const crmHeaders: IHeaders = {
            headers: {
                [X_PARLEM_API_KEY]: import.meta.env.VITE_API_KEY_SHOPPINGCART,
                [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON
            }
        }
        const response: IApiRes<IUser> = await axios.get(url, crmHeaders)

        return response && response.data
    },
    
}

export default usersService