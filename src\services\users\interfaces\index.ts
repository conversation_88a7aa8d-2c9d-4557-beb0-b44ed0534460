import type { IAuth } from './auth.interface'
import type { ICompany } from './company.interface'
import type { IUser } from './user.interface'
import type { IAddress } from './address.interface'
import type { IBillingInfo } from './billing-info.interface'
import type { IPersonalData } from './personal-data.interface'
import type { IGdprRegistry } from './gdpr-registry.interface'
import type { IAdvertisingConfig } from './advertising-config.interface'
import type { IContact } from './contact.interface'

export type {
  IAuth,
  ICompany,
  IUser,
  IAddress,
  IBillingInfo,
  IPersonalData,
  IGdprRegistry,
  IAdvertisingConfig,
  IContact
}
