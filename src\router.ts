import { PersonalData, CheckCoverage, BillingData, Catalog, ResumData } from '@/components'
import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router'

const routes = [
    { path: '/', redirect: '/personal-data' },
    { path: '/personal-data', component: PersonalData, name:'personal-data' },
    { path: '/coverage', component: CheckCoverage, name:'coverage' },
    { path: '/products', component: Catalog, name:'products' },
    { path: '/billing-data', component: BillingData, name:'billing-data' },
    { path: '/resum-data', component: ResumData, name:'resum-data' },
    { path: '/**', redirect: 'personal-data' },
]
const router = createRouter({
    history: createWebHashHistory(),
    routes
})

export default router
