{"home": {"header": "Welcome to the Vue 3 I18n!"}, "test": {"header": "Test webcomponent en"}, "coverage_form": {"check_coverage": "Check coverage"}, "coverage": {"description": "By providing your address we will be able to know if you have coverage"}, "personal-data": {"title": "Personal data", "description": "To get started we need your data", "name": "Name", "surname": "Surnames", "second-surname": "Second surname", "phone": "Phone", "phone-placeholder": "Contact number", "email": "Email", "email-placeholder": "Email address", "id-number": "DNI or NIE", "nationality": "Nationality", "nationality-placeholder": "Select your nationality", "birthdate": "Birthdate", "birthdate-placeholder": "dd-mm-yyyy", "user": "User", "password": "Password", "reset-password": "Reset password", "create-account": "Create account", "new-customer": "New customer", "old-customer": "I am already a customer", "customer-type": "Self-employed", "company-name": "Company data", "cif-number": "CIF number", "business-name": "Business name", "admin-name": "Administrator's first name", "admin-surname": "Administrator's last name", "admin-id-number": "Administrator's ID number", "admin-doc-type": "Document type", "foundation-date": "Company foundation date", "login-error": "An error occurred, login failed.", "particular": "Particular", "business": "Business"}, "streetType": {"all": "All", "other": "Others", "avenue": "Avenue", "bulevar": "Bulevar", "crossing": "Crossing", "gardens": "Gardens", "highway": "Highway", "neighborhood": "Neighborhood", "passage": "Passage", "park": "Park", "patrol": "Patrol", "placette": "Placette", "polygon": "Polygon", "promenade": "Promenade", "road": "Road", "roundabout": "Roundabout", "sector": "Sector", "settlement": "Settlement", "square": "Square", "street": "Street", "suburb": "Suburb", "track": "Track", "urbanization": "Urbanization"}, "billing-data": {"title": "Billing data", "description": "Billing address", "description-shipping": "Billing and shipping address", "send": "Send", "clear": "Remove", "invalidInputValue": "The current value is not valid", "sim-address-message": "I want a different shipping address than the billing address.", "sim": " SIM card", "and": " and", "sim-address": "Shipping address of", "agile-address-message": "I want to receive the Agile Tv smartbox at an address other than the billing address.", "agile-address": "Agile Tv Shipping Address", "agile-title": " Agile Tv", "agile-account-email": "Agile Tv email account", "authorize-person-message": "Vull autoritzar la recollida de la targeta SIM a una altra persona.", "authorized-person": "Authorized person", "owner-account": "Account holder", "owner-account-message": "It must match the contract holder", "owner-account-dni": "ID of the account holder", "account-number": "Bank account number (IBAN)", "account-number-message": "The IBAN number is a 24-digit code that identifies your bank account anywhere in the world.", "invoice-communication": "You will receive your invoice to your email address", "discounts": "Discounts", "select-discount": "Select discount"}, "resum-data": {"title": "Summary of your order", "personal-data": "Personal data", "name-surname": "Name and surnames", "name-company": "Company name", "birthdate": "Birthdate", "email": "Email", "phone": "Phone", "coverage": "Installation data", "coverage-street": "Installation address", "lines": "Lines", "fix": "Fix", "nationality": "Nationality", "docNum": "Docum", "fiber": "Fibra", "mobile": "Mobile", "additional-mobile": "Additional mobile", "tv": "Television", "switchboard": "Switchboard product", "tv-user": "Television user", "addon": "Additional product", "billing-data": "Billing data", "billing-street": "Billing address", "sim": "Shipping address", "iban": "IBAN", "price": "Price", "product": "Contracted products", "eur": "€/month", "eur-without": "€/month vat not included", "new-line": "<PERSON><PERSON><PERSON>", "portability": "Portability of the line", "validation-action": "I validate the information and accept", "sim-address-check-label": "I accept the conditions of contracting.", "sim-address-check-label-url": "Read.", "error": "Oops! Something has gone wrong.", "eror-later": "Please try again later.", "waiting-message": "Placing order. This process may take a few minutes to complete.", "success": "We inform you that your order has been successfully placed", "success-web-1": "There's only one step left!", "success-web-2": "Access your email to validate your identity.", "thanks": "Thank you so much!", "total-price": "Total price", "scheduling-title": "Schedule your fiber instalation", "scheduling-manual": "Log in to MySim to schedule your fiber installation", "scheduling-automatic": "We will contact the customer shortly to schedule the installation of the fiber", "scheduling-manual-new": "The customer needs to validate their identity before you can login to MySim to schedule your fiber installation", "scheduling-automatic-new": "Once the customer has validated their identity, we will contact them to schedule the installation of the fiber", "product-conditions": "Product conditions", "price-offer": " for {months} month, then {finalPrice}€/month", "authorized-person-name": "Name of authorized person", "authorized-person-phone": "Phone of authorized person", "customer-type": "Customer type", "CustomerResidential": "Residential", "CustomerFreelance": "Freelance"}, "actions": {"next": "Next", "prev": "Prev", "sign-in": "Sign in", "save": "Save", "edit": "Edit", "goto-main-page": "Go to main page", "edit-cart": "Modify shopping cart", "print-summary": "Print summary"}, "errors": {"required": "This value is required", "email": "<PERSON><PERSON> is invalid", "phone": "The phone is invalid", "isValidDoc": "The document is not valid, it must be a NIF, NIE, CIF or passport", "alpha": "Invalid format, can only contain letters", "alphaNum": "Invalid format, only allows alphanumeric characters", "numeric": "Invalid format, only supports numeric characters", "general-error": "There has been an error", "username": "Review the username entered", "account": "Your user has not been recognized. Verify that you allow pop-up windows and that you have logged in correctly."}, "stepper": {"personal-data": "Personal data", "products": "Products", "billing-data": "Billing data", "coverage": "Coverage", "confirmation": "Confirmation"}, "tariffForm": {"others": "Others"}, "catalog": {"set-family-without-coverage-info": "To offer you the best price, we need to know where you want to install the fiber.", "set-family-without-coverage-answer": "Do you want to continue?", "confirm-set-coverage": "Yes", "cancel-set-coverage": "No"}, "banner": {"price": "Price", "mobile": "Mobile", "fixFiber": "Fix + Fiber"}}