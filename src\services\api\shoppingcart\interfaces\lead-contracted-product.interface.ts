export interface ILeadContractedProduct {
  id: string | number
  productName: string
  productCode: string
  paymentMethod?: string | null
  rootProduct?: number | null
  fiber: {
    number?: string | null
    territoryOwner?: string | null
    provider?: string | null
    installationAddress?: {
      gescal37?: string | null
      gescal17?: string | null
      country?: string | null
      state?: string | null
      province?: string | null
      city?: string | null
      streetType?: string | null
      street?: string | null
      number?: string | null
      specification?: string | null
      zip?: string | null
    }
  } | null
  mobile?: {
    iccParlem?: string | null
    portability?: {
      currentPaymentMethod?: string | null
      currentICC?: string | null
      donorOperatorCode?: string | null
    } | null
    numberInfo?: {
      number?: string | null
      additionalInfo?: string | null
    } | null
  } | null
  tv?: {
    tvUser?: string
  } | null
  switchBoard?: {
    landline: string
  } | null
  landLine?: {
    portability?: {
      currentPaymentMethod?: string | null
      currentICC?: string | null
      donorOperatorCode?: string | null
    } | null
    number?: string | null
  } | null
}
