<template id="billing-data">
  <PwSeparator :title="t('billing-data.title')"></PwSeparator>
  <main v-if="!loading" class="p-4">
    <div class="flex mb-2 pt-6 justify-between border-b border-primary w-full items-center">
      <p v-if="checkedSimAddress" class="text-base font-bold">
        {{ t('billing-data.description') }}
      </p>
      <p v-else class="text-base font-bold">{{ t('billing-data.description-shipping') }}</p>
    </div>
    <div
      v-if="!isEmptyAddress && currBillingData.billingAddress && !isDefaultBillingAddressZero"
      class="py-2 pb-6 flex items-center justify-between"
    >
      <div>
        {{ setAddressString(currBillingData.billingAddress, lang) }}
      </div>

      <div class="cursor-pointer flex items-center text-primary" @click="toggleEditAddress">
        <span class="text-sm z-10 -mr-2">{{ t('actions.edit') }}</span
        ><img :src="editIcon" width="50" />
      </div>
    </div>
    <div v-if="isEmptyAddress || showEditAddress || isDefaultBillingAddressZero" class="py-4">
      <PwStreetMap
        :lang="lang"
        :address="currBillingData.billingAddress"
        from="billingAddress"
        @saveAddress="saveBillingAddress"
      >
      </PwStreetMap>
    </div>
    <div v-if="showSimAddressBlock" class="pb-6">
      <PwCheck
        :lang="lang"
        name="shippingAddress"
        v-model="checkedSimAddress"
        :checked="checkedSimAddress"
        labelClass="text-secondary"
        :label="t('billing-data.sim-address-message')"
      />
      <div v-if="checkedSimAddress">
        <div
          v-if="
            checkedSimAddress &&
            (currentShippingAddress.number || currentShippingAddress.streetNumber)
          "
          class="flex mb-2 pt-6 justify-between border-b border-primary w-full items-center"
        >
          <p class="text-base font-bold">
            {{ t('billing-data.sim-address')
            }}<span v-if="hasSim()"> {{ t('billing-data.sim') }}</span>
            <span v-if="hasSim() && hasAgileTv()"> {{ t('billing-data.and') }}</span>
            <span v-if="hasAgileTv()"> {{ t('billing-data.agile-title') }}</span>
          </p>
        </div>
        <div
          v-if="
            checkedSimAddress &&
            (currentShippingAddress.number || currentShippingAddress.streetNumber)
          "
          class="flex pt-2 items-center justify-between"
        >
          <div>{{ setAddressString(currentShippingAddress, lang) }}</div>
          <div class="cursor-pointer flex items-center text-primary" @click="toggleSimAddress">
            <span class="text-sm z-10 -mr-2">{{ t('actions.edit') }}</span
            ><img :src="editIcon" width="50" />
          </div>
        </div>
        <div
          v-if="
            (!currentShippingAddress ||
              (!currentShippingAddress.number && !currentShippingAddress.streetNumber) ||
              isVisibleSimAddress) &&
            checkedSimAddress
          "
        >
          <div
            v-if="
              !currentShippingAddress ||
              (!currentShippingAddress.number && !currentShippingAddress.streetNumber)
            "
            class="flex mb-2 pt-6 justify-between border-b border-primary w-full items-center"
          >
            <p class="text-base font-bold">{{ t('billing-data.sim-address') }}</p>
          </div>
          <PwStreetMap
            :lang="lang"
            :address="currentShippingAddress"
            from="shippingAddress"
            @saveAddress="saveShippingAddress"
            class="pt-4"
          >
          </PwStreetMap>
        </div>
        <!-- <div class="mt-4">
          <PwCheck
            :lang="lang"
            class="mt-6"
            name="authorizedPerson"
            v-model="checkedAuthorizePerson"
            :checked="checkedAuthorizePerson"
            labelClass="text-secondary mt-6"
            :label="t('billing-data.authorize-person-message')"
          />
        </div> -->

        <!-- <div v-if="checkedAuthorizePerson">
          <div class="flex mb-2 pt-6 justify-between border-b border-primary w-full items-center">
            <p class="text-base font-bold">
              {{ t('billing-data.authorized-person') }}
            </p>
          </div>
          <div>
            <div v-if="infoAuthorize.name || infoAuthorize.phone" class="flex justify-between">
              <div class="mt-4 flex-1">
                <p>{{ t('resum-data.authorized-person-name') }}: {{ infoAuthorize.name }}</p>
                <p>{{ t('resum-data.authorized-person-phone') }}: {{ infoAuthorize.phone }}</p>
              </div>
              <div class="cursor-pointer flex items-center text-primary">
                <span class="text-sm z-10 -mr-2" @click="toggleAuthorizedPerson">{{ t('actions.edit') }}</span>
                <img src="../../assets/svg/editar.svg" width="50" />
              </div>
            </div>

            <div v-if="isVisibleAuthorizedPerson">
              <div class="grid gap-6 md:grid-cols-2 mb-2 mt-4">
                <PwInputText
                  :lang="lang"
                  ref="authorizedPersonRef.name"
                  :required="true"
                  :validations="['required', 'name']"
                  :label="t('personal-data.name')"
                  v-model="authorizedPerson.name"
                  :value="authorizedPerson.name"
                  :name="'name'"
                  :placeholder="t('personal-data.name')"
                >
                </PwInputText>
                <PwInputText
                  :lang="lang"
                  ref="authorizedPersonRef.phone"
                  :required="true"
                  :validations="['required', 'phone']"
                  :label="t('personal-data.phone')"
                  v-model="authorizedPerson.phone"
                  :value="authorizedPerson.phone"
                  :name="'phone'"
                  :placeholder="t('personal-data.phone')"
                >
                </PwInputText>
              </div>

              <div class="flex flex-wrap-reverse max-h-full justify-end mt-2">
                <div class="w-full md:w-2/4 md:pl-3">
                  <PwButton
                    theme="secondary"
                    @click="saveAuthorizedPerson"
                    :text="t('actions.save')"
                  ></PwButton>
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
    <div>
      <div class="flex mb-4 pt-6 justify-between border-b border-primary w-full items-center">
        <p class="text-base font-bold">{{ t('billing-data.owner-account') }}</p>
      </div>
      <div class="flex items-center justify-between">
        <div class="flex flex-col mb-4 mt-2">
          <span>{{ currBillingData?.cccOwner }}</span>
          <span class="mt-3">{{ currBillingData?.cccOwnerIdentification }}</span>
        </div>

        <div
          class="cursor-pointer flex items-center text-primary"
          @click="toggleAccountHolder(false)"
        >
          <span class="text-sm z-10 -mr-2">{{ t('actions.edit') }}</span
          ><img :src="editIcon" width="50" />
        </div>
      </div>
      <div v-if="isVisibleAccountHolder">
        <PwInputText
          :lang="lang"
          v-model="accountHolder"
          ref="accountHolderRef"
          :required="true"
          :validations="['required']"
          :label="t('billing-data.owner-account')"
          :name="'accountHolder'"
          :placeholder="t('billing-data.owner-account')"
        >
        </PwInputText>

        <p class="text-sm mt-1">
          {{ t('billing-data.owner-account-message') }}
        </p>

        <PwInputText
          :lang="lang"
          v-model="ownerAccountHolder"
          ref="ownerAccountHolderRef"
          :required="true"
          :validations="['required', 'dni']"
          :label="t('billing-data.owner-account-dni')"
          :name="'ownerAccountHolder'"
          :placeholder="t('billing-data.owner-account-dni')"
          class="mt-3"
        >
        </PwInputText>
        <div class="flex flex-wrap-reverse max-h-full justify-end mt-2">
          <div class="w-full md:w-2/4 md:pr-3 mb-2">
            <PwButton
              theme="secondary"
              @click="saveAccountHolder"
              :text="t('actions.save')"
              :disabled="!(isValidAccountHolder && accountHolder.length)"
            ></PwButton>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="flex mb-2 pt-6 justify-between border-b border-primary w-full items-center">
        <p class="text-base font-bold">{{ t('billing-data.account-number') }}</p>
      </div>
      <div class="flex items-center justify-between my-4">
        <div>
          {{ currBillingData?.iban }}
        </div>
        <div
          v-if="currBillingData?.iban"
          class="cursor-pointer flex items-center text-primary"
          @click="toggleBankAccount(false)"
        >
          <span class="text-sm z-10 -mr-2">{{ t('actions.edit') }}</span
          ><img :src="editIcon" width="50" />
        </div>
      </div>
      <div v-if="isVisibleBankAccount">
        <PwInputText
          :lang="lang"
          ref="bankAccountRef"
          :required="true"
          :validations="['required', 'iban']"
          :label="t('billing-data.account-number')"
          v-model="bankAccount"
          :value="bankAccount"
          :name="'accountHolder'"
          placeholder="ES00 0000 0000 0000 0000 0000"
        >
        </PwInputText>
        <p class="text-sm mt-1">
          {{ t('billing-data.account-number-message') }}
        </p>
        <div class="flex flex-wrap-reverse max-h-full justify-end mt-2">
          <div class="w-full md:w-2/4 md:pr-3 mb-2">
            <PwButton
              theme="secondary"
              @click="saveBankAccount"
              :text="t('actions.save')"
              :disabled="!(isValidBankAccount && bankAccount.length)"
            ></PwButton>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="
        [channelDealers, channelShops, channelCallCenter].includes(channel) && discounts?.length
      "
    >
      <div class="flex mb-2 pt-6 justify-between border-b border-primary w-full items-center">
        <p class="text-base font-bold">{{ t('billing-data.discounts') }}</p>
      </div>
      <PwSelectAutocomplete
        :lang="lang"
        :label="t('billing-data.select-discount')"
        :placeholder="t('billing-data.select-discount')"
        name="street_type"
        :items="discounts"
        itemTitle="label"
        itemValue="value"
        :value="selectedDiscount?.label"
        v-model="selectedDiscount"
        :filterValueOnFocus="false"
        classSelectorHeight="max-h-36"
      />

      <!-- <div class="flex items-center justify-between my-4">
        <div>
          {{ currBillingData?.iban }}
        </div>
        <div
          v-if="currBillingData?.iban"
          class="cursor-pointer flex items-center text-primary"
          @click="toggleBankAccount(false)"
        >
          <span class="text-sm z-10 -mr-2">{{ t('actions.edit') }}</span><img :src="editIcon" width="50" />
        </div>
      </div> -->
    </div>
    <div class="pb-0 pt-8">
      <p class="flex -ml-4 items-center justify-start">
        <img :src="billIcon" />

        {{ t('billing-data.invoice-communication') }}
      </p>
    </div>
    <div class="py-4">
      <div class="flex justify-between items-center">
        <PwButton @click="prevStep" theme="light" class="w-[60px] max-w-[60px] h-[40px] mr-2">
          <font-awesome-icon icon="fa-solid fa-angle-left" class="max-w-[15px] text-gray" />
        </PwButton>
        <PwButton
          @click="saveBillingData"
          :text="t('actions.next')"
          :disabled="!isValidFormData"
        ></PwButton>
      </div>
    </div>

    <div class="input-errors" v-for="error of v$.$errors" :key="error.$uid">
      <div class="text-sm text-error">{{ t('errors.' + error.$validator) }}</div>
    </div>
  </main>
  <div v-if="loading" class="flex justify-center mt-10">
    <font-awesome-icon icon="fa-solid fa-spinner" class="max-w-[20px] text-info" />
  </div>
</template>

<script lang="ts">
import en from '@/i18n/locales/en.json'
import es from '@/i18n/locales/es.json'
import ca from '@/i18n/locales/ca.json'
import gl from '@/i18n/locales/gl.json'
import { useI18n } from 'vue-i18n'
import { Store, useStore } from 'vuex'
import {
  PwButton,
  PwSeparator,
  PwInputText,
  PwCheck,
  PwSelectAutocomplete
} from 'parlem-webcomponents-common'
import { useVuelidate } from '@vuelidate/core'
import { ref, type Ref, watch, computed, onMounted, onBeforeMount } from 'vue'
import {
  GET_ACCOUNT_ID,
  GET_BILLING_DATA,
  SET_CHECKED_SIM_ADDRESS,
  SET_ACCOUNT_HOLDER,
  SET_BANK_ACCOUNT,
  SET_BILLING_DATA,
  SET_SIM_ADDRESS,
  SHOPPING,
  GET_SIM_ADDRESS,
  SET_BILLING_ADDRESS,
  GET_TRADEMARK,
  GET_DISCOUNTS,
  SET_SELECTED_DISCOUNT
  // SET_CHECKED_AUTHORIZE_PERSON,
  // SET_AUTHORIZED_PERSON
} from '@/store/modules/shopping/constants/store.constants'
import type { IAddressForm } from '@/components/billing-address/interfaces/address-form.interface'
import type { IAddress, IUser } from '@/services/users/interfaces'
import PwStreetMap from '@/components/billing-address/PwStreetMap.vue'
import type { IBillingData } from '@/services/users/interfaces/billing-data.interface'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import { leadsService, stepperService } from '@/services'
import type { ILeadCandidate } from '@/interfaces/lead-candidate'
import type { IStep } from '@/interfaces/step.interface'
import { setAddressString } from 'parlem-webcomponents-common'
import type { ICatalog } from '@/interfaces/catalog.interface'
import {
  CHANNEL_WEB,
  CHANNEL_DEALERS,
  CHANNEL_SHOPS,
  CHANNEL_CALL_CENTER
} from '@/services/constants/services.constants'
import type { IPickListValue } from '@/services/api/crm/interfaces'

export default {
  name: 'billing-data',
  components: {
    PwInputText,
    PwSeparator,
    PwButton,
    PwStreetMap,
    PwCheck,
    PwSelectAutocomplete
  },
  methods: {
    setAddressString(address: ICoverage | IAddress, lang: string) {
      return setAddressString(address, lang)
    }
  },
  setup(props: any) {
    const store: Store<any> = useStore()
    const v$ = useVuelidate()
    const lang: Ref<string> = computed(() => store.getters[`${SHOPPING}/getLang`])
    const { t, locale }: any = useI18n({
      messages: {
        en,
        gl,
        es,
        ca
      }
    })
    locale.value = lang.value || locale.value
    const currBillingData: Ref<IBillingData> = computed(
      () => store.getters[`${SHOPPING}/getBillingData`]
    )
    const coverage: Ref<ICoverage> = computed(() => store.getters[`${SHOPPING}/getCoverage`])
    const defaultBillingAddress: Ref<IAddress> = computed(
      () => store.getters[`${SHOPPING}/getDefaultBillingAddress`]
    )
    const defaultShippingAddress: Ref<IAddress> = computed(
      () => store.getters[`${SHOPPING}/getDefaultShippingAddress`]
    )
    const trademark: Ref<string> = computed(() => store.getters[`${SHOPPING}/${GET_TRADEMARK}`])
    const discounts: Ref<IPickListValue[]> = computed(
      () => store.getters[`${SHOPPING}/${GET_DISCOUNTS}`]
    )
    const billIcon = `${import.meta.env.VITE_ICONS_URL}/bill-black.svg`
    const editIcon = `${import.meta.env.VITE_ICONS_URL}/edit-${trademark.value.toLowerCase()}.svg`
    let bankAccountRef = ref<any>(null)
    let bankAccount: Ref<string> = ref('')
    let selectedDiscount: Ref<IPickListValue> = ref({
      label: 'No aplicar cap descompte',
      value: null,
      active: true
    })
    /*       store.getters[`${SHOPPING}/${GET_DISCOUNTS}`][0]
     */
    let isVisibleBankAccount: Ref<boolean> = ref(false)
    const isValidBankAccount: Ref<boolean> = ref(false)
    let accountHolderRef = ref<any>(null)
    let accountHolder: Ref<string> = ref('')
    let ownerAccountHolderRef = ref<any>(null)
    let ownerAccountHolder: Ref<string> = ref('')
    let isVisibleAccountHolder: Ref<boolean> = ref(false)
    const isValidAccountHolder: Ref<boolean> = ref(false)
    let isValidFormData: Ref<boolean> = ref(false)
    const newUser: Ref<IUser> = computed(() => store.getters[`${SHOPPING}/getNewUser`])
    const showEditAddress: Ref<boolean> = ref(false)
    const isEmptyAddress: Ref<boolean> = ref<boolean>(true)
    const isDefaultBillingAddressZero: Ref<boolean> = ref<boolean>(false)
    const billingAddress: Ref<Partial<IAddressForm>> = ref<Partial<IAddressForm>>({})
    const channelWeb = ref<any>(CHANNEL_WEB)
    const channelDealers = ref<any>(CHANNEL_DEALERS)
    const channelShops = ref<any>(CHANNEL_SHOPS)
    const channelCallCenter = ref<any>(CHANNEL_CALL_CENTER)

    const oldUser: Ref<IUser> = computed(() => store.getters[`${SHOPPING}/getOldUser`])
    const currentShippingAddress: Ref<IAddressForm> = computed(
      () => store.getters[`${SHOPPING}/getSimAddress`]
    )
    const catalog: Ref<ICatalog[]> = computed(() => store.getters[`${SHOPPING}/getCatalog`])
    const channel: Ref<IUser> = computed(() => store.getters[`${SHOPPING}/getChannel`])
    let checkedSimAddress: Ref<boolean> = ref(store.getters[`${SHOPPING}/getCheckedSimAddress`])
    let isVisibleSimAddress: Ref<boolean> = ref(false)

    // let checkedAuthorizePerson: Ref<boolean> = ref(
    //   store.getters[`${SHOPPING}/getCheckedAuthorizedPerson`]
    // )
    // const infoAuthorize = computed(() => store.getters[`${SHOPPING}/getAuthorizedPerson`])
    // let isVisibleAuthorizedPerson: Ref<boolean> = ref(false)
    // const isValidAuthorizedPerson: Ref<boolean> = ref(false)
    // let authorizedPerson = { name: '', phone: '' }
    // let authorizedPersonRef = ref<any>(null)

    let showSimAddressBlock: Ref<boolean> = ref(false)
    const shippingAddress: Ref<Partial<IAddressForm>> = ref<Partial<IAddressForm>>({})
    let loading: Ref<boolean> = ref<boolean>(false)

    onBeforeMount(async () => {
      loading.value = true
      await loadDataFromStore()
      setShowSimAddressBlockValue()
      loading.value = false
    })

    onMounted((): void => {
      initBillingAddress()
      setSimAddress()
      initAccountHolder()
      initBankAccount()
      // initVisibility()
    })

    // TO DO A RESUM DATA
    function setShowSimAddressBlockValue(): void {
      showSimAddressBlock.value = hasSim() || hasAgileTv()
    }
    function hasSim(): boolean {
      return catalog.value.some((rate: any) =>
        rate.contractedProducts.find(
          (product: any) => product.mobile || product.additionalMobileLines?.length
        )
      )
    }
    function hasAgileTv(): boolean {
      return catalog.value.some((rate: any) =>
        rate.contractedProducts.find((product: any) => product.tv)
      )
    }

    async function loadDataFromStore(): Promise<void> {
      initBankAccount()
      await initAccountHolder()
    }

    function initBankAccount(): void {
      isVisibleBankAccount.value = currBillingData.value?.iban ? false : true
      isValidForm()
    }

    // function initVisibility() {
    //   isVisibleAuthorizedPerson.value = !infoAuthorize.value.name && !infoAuthorize.value.phone
    // }

    function isValidForm() {
      isValidFormData.value =
        !!currBillingData.value?.iban &&
        !!currBillingData.value.cccOwner &&
        (!checkedSimAddress.value ||
          (checkedSimAddress.value &&
            (!!currentShippingAddress.value?.number ||
              !!currentShippingAddress.value?.streetNumber ||
              !!currentShippingAddress.value?.fullAddress))) &&
        (!!currBillingData.value?.billingAddress?.number ||
          !!currBillingData.value?.billingAddress?.streetNumber ||
          !!currBillingData.value?.billingAddress?.fullAddress) &&
        // (!checkedAuthorizePerson.value ||
        //   (checkedAuthorizePerson.value &&
        //     infoAuthorize.value.name &&
        //     infoAuthorize.value.phone)) &&
        !isDefaultBillingAddressZero.value
    }

    function saveBillingAddress(address: ICoverage, isChanged: boolean = true): void {
      if (!address?.from || address.from === 'billingAddress') {
        store.dispatch(`${SHOPPING}/${SET_BILLING_ADDRESS}`, { billingAddress: address, isChanged })
        isEmptyAddress.value = false
        showEditAddress.value = false
        isDefaultBillingAddressZero.value = false
        isValidForm()
      }
    }

    function saveShippingAddress(address: ICoverage) {
      if (!address?.from || address.from === 'shippingAddress') {
        isVisibleSimAddress.value = false
        store.dispatch(`${SHOPPING}/${SET_SIM_ADDRESS}`, address)
        isValidForm()
      }
    }

    watch(checkedSimAddress, (isChecked: boolean) => {
      store.dispatch(`${SHOPPING}/${SET_CHECKED_SIM_ADDRESS}`, isChecked)
      if (
        isChecked &&
        !isVisibleSimAddress.value &&
        (!currentShippingAddress.value?.number || !currentShippingAddress.value?.streetNumber)
      ) {
        isVisibleSimAddress.value = true
      }
      if (!isChecked) {
        isVisibleSimAddress.value = false
      }
      setSimAddress()
      isValidForm()
    })

    // watch(checkedAuthorizePerson, (isChecked: boolean) => {
    //   store.dispatch(`${SHOPPING}/${SET_CHECKED_AUTHORIZE_PERSON}`, isChecked)
    //   /*       if (isChecked && !isVisibleSimAddress.value && !currentShippingAddress.value?.street) {
    //     isVisibleSimAddress.value = true
    //   }
    //   if (!isChecked) {
    //     isVisibleSimAddress.value = false
    //   } */
    //   setSimAddress()
    //   isValidForm()
    // })

    watch(bankAccount, (newBankAccount) => {
      isValidBankAccount.value = bankAccountRef?.value?.$v() ? false : true
    })

    watch(accountHolder, (newAccountHolder) => {
      isValidAccountHolder.value = accountHolderRef?.value?.$v() ? false : true
    })

    async function initAccountHolder(): Promise<void> {
      let accHolder = {
        cccOwner: '',
        cccOwnerIdentification: ''
      }

      if (currBillingData.value?.cccOwner) {
        accHolder.cccOwner = currBillingData.value.cccOwner
      } else if (newUser.value?.personalData?.documentType === 'CIF') {
        accHolder.cccOwner = `${newUser.value.companyStructure?.companyManagerFirstName} ${newUser.value.companyStructure?.companyManagerLastName}`
      } else if (newUser.value?.personalData?.firstName) {
        accHolder.cccOwner = `${newUser.value.personalData.firstName} ${newUser.value.personalData.lastName}`
      }

      await store.dispatch(`${SHOPPING}/${SET_ACCOUNT_HOLDER}`, accHolder)

      isValidForm()
    }

    function initBillingAddress(): void {
      if (!isEmptyAddress.value) {
        return saveBillingAddress(currBillingData.value.billingAddress, false)
      }
      if (currBillingData.value && currBillingData.value.billingAddress?.street) {
        return saveBillingAddress(currBillingData.value.billingAddress, false)
      }
      if (defaultBillingAddress.value) {
        if (
          defaultBillingAddress.value?.number === '' ||
          defaultBillingAddress.value?.number === null
        ) {
          isDefaultBillingAddressZero.value = true
        } else {
          return saveBillingAddress(defaultBillingAddress.value, false)
        }
      }
      const selecteTariff: any = catalog.value.find((tariff: any) =>
        tariff.contractedProducts.some((product: any) => product.fiber?.installationAddress)
      )
      const coverage: ICoverage | undefined = selecteTariff?.contractedProducts.find(
        (product: any) => product?.fiber?.installationAddress
      )?.fiber?.installationAddress
      if (coverage) {
        return saveBillingAddress(coverage, false)
      }
    }

    function setSimAddress(): void {
      const coverage: ICoverage | undefined = catalog.value.find(
        (product: any) => product.installationAddress
      )?.installationAddress
      if (
        currentShippingAddress.value &&
        (currentShippingAddress.value.number || currentShippingAddress.value.streetNumber)
      ) {
        return saveShippingAddress(currentShippingAddress.value)
      }
      if (defaultShippingAddress.value) {
        return saveShippingAddress(defaultShippingAddress.value)
      }
      if (!checkedSimAddress.value) {
        return saveShippingAddress(currBillingData.value.billingAddress)
      }
      if (coverage) {
        return saveShippingAddress(coverage)
      }
    }

    function toggleSimAddress(): void {
      isVisibleSimAddress.value = !isVisibleSimAddress.value
    }

    function toggleEditAddress(): void {
      showEditAddress.value = !showEditAddress.value
    }

    function toggleAccountHolder(saved: boolean): void {
      isVisibleAccountHolder.value = !isVisibleAccountHolder.value
      accountHolder.value = saved ? accountHolder.value : ''
      isValidAccountHolder.value = accountHolderRef?.value?.v$?.$invalid ? false : true
    }

    function toggleBankAccount(saved: boolean): void {
      isVisibleBankAccount.value = !isVisibleBankAccount.value
      bankAccount.value = saved ? bankAccount.value : ''
      isValidBankAccount.value = bankAccountRef?.value?.$v()
    }

    // function toggleAuthorizedPerson(): void {
    //   isVisibleAuthorizedPerson.value = !isVisibleAuthorizedPerson.value
    // }

    function saveAccountHolder(): void {
      const accountHolderData = {
        cccOwner: accountHolder.value,
        cccOwnerIdentification: ownerAccountHolder.value
      }

      store.dispatch(`${SHOPPING}/${SET_ACCOUNT_HOLDER}`, accountHolderData)
      toggleAccountHolder(true)
      isValidForm()
    }

    function saveBankAccount(): void {
      const cleanedValue = bankAccount.value.replace(/\s+/g, '')
      store.dispatch(`${SHOPPING}/${SET_BANK_ACCOUNT}`, cleanedValue)
      toggleBankAccount(true)
      isValidForm()
    }

    // function saveAuthorizedPerson() {
    //   store.dispatch(`${SHOPPING}/${SET_AUTHORIZED_PERSON}`, authorizedPerson)
    //   isVisibleAuthorizedPerson.value = false
    //   isValidForm()
    // }

    function saveBillingData(): void {
      const currentStep: number = store.getters[`${SHOPPING}/getCurrentStep`]
      store.dispatch(`${SHOPPING}/${SET_SELECTED_DISCOUNT}`, selectedDiscount.value)
      createLeadCandidate()
      setCompletedStep(currentStep)
      nextStep()
    }

    function nextStep(): void {
      stepperService.nextStep()
    }

    function prevStep(): void {
      stepperService.prevStep()
    }

    function setCompletedStep(currentStep: number): void {
      stepperService.setCompletedStep(currentStep)
    }

    async function createLeadCandidate(): Promise<void> {
      const leadCandidate: ILeadCandidate = getLeadCandidateData()
      await leadsService.createLeadCandidate(leadCandidate)
    }

    function getLeadCandidateData(): ILeadCandidate {
      const step: number = store.getters[`${SHOPPING}/getCurrentStep`]
      const steps: IStep[] = store.getters[`${SHOPPING}/getSteps`]
      const stepData: IStep | undefined = steps.find((st: IStep) => st.label === step.toString())
      const position: string =
        channel.value !== channelWeb.value
          ? 'Confirmed'
          : stepData
          ? `${stepData.label} - ${stepData.name}`
          : `${step}`
      const accountId: string = store.getters[`${SHOPPING}/${GET_ACCOUNT_ID}`]
      const billingData: IBillingData = store.getters[`${SHOPPING}/${GET_BILLING_DATA}`]
      const shippingAddress: IAddressForm = store.getters[`${SHOPPING}/${GET_SIM_ADDRESS}`]
      const billingAddres: IAddressForm = billingData.billingAddress

      const newUser: IUser = store.getters[`${SHOPPING}/getNewUser`]
      const userDefaultContact = store.getters[`${SHOPPING}/getUserDefaultContact`]

      let lead: ILeadCandidate = {
        accountId,
        phone: userDefaultContact?.phone,
        email: userDefaultContact?.email,
        firstName: newUser.personalData.firstName,
        lastName1: newUser.personalData.lastName,
        fiscalIdNumber: newUser.personalData.documentNumber,
        birthdate: newUser.personalData.personBirthdate,
        position,
        customDiscount: selectedDiscount.value.value,
        billingCity: billingAddres?.location,
        billingStreet: `${billingAddres?.street} ${
          billingAddres?.number || billingAddres?.streetNumber
        } ${billingAddres?.block} ${billingAddres?.letter} ${billingAddres?.floor} ${
          billingAddres?.door
        } ${billingAddres?.firstHand} ${billingAddres?.secondHand} ${billingAddres?.fullAddress}`,
        billingState: billingAddres?.state,
        billingPostalCode: billingAddres?.postalCode || billingAddres.zip
      }
      if (shippingAddress?.street) {
        lead = {
          ...lead,
          shippingStreet: `${shippingAddress?.street} ${
            shippingAddress?.number || shippingAddress?.streetNumber
          } ${shippingAddress?.block} ${shippingAddress?.letter} ${shippingAddress?.floor} ${
            shippingAddress?.firstHand
          } ${shippingAddress?.secondHand} ${billingAddres?.fullAddress}`,
          shippingPostalCode: shippingAddress?.postalCode,
          shippingCity: shippingAddress?.location
        }
      }

      return lead
    }

    return {
      t,
      v$,
      store,
      saveBillingData,
      location,
      toggleAccountHolder,
      toggleBankAccount,
      isVisibleAccountHolder,
      isVisibleBankAccount,
      accountHolder,
      ownerAccountHolder,
      bankAccount,
      saveAccountHolder,
      saveBankAccount,
      bankAccountRef,
      isValidBankAccount,
      isValidAccountHolder,
      isValidForm,
      coverage,
      currBillingData,
      isValidFormData,
      accountHolderRef,
      ownerAccountHolderRef,
      isEmptyAddress,
      showEditAddress,
      isDefaultBillingAddressZero,
      toggleEditAddress,
      billingAddress,
      showSimAddressBlock,
      checkedSimAddress,
      // checkedAuthorizePerson,
      // authorizedPerson,
      // authorizedPersonRef,
      // toggleAuthorizedPerson,
      // isValidAuthorizedPerson,
      isVisibleSimAddress,
      // isVisibleAuthorizedPerson,
      currentShippingAddress,
      shippingAddress,
      toggleSimAddress,
      prevStep,
      lang,
      saveBillingAddress,
      saveShippingAddress,
      hasSim,
      hasAgileTv,
      channel,
      channelWeb,
      channelDealers,
      channelShops,
      channelCallCenter,
      // saveAuthorizedPerson,
      // infoAuthorize,
      loading,
      billIcon,
      editIcon,
      discounts,
      selectedDiscount
      // initVisibility
    }
  }
}
</script>
