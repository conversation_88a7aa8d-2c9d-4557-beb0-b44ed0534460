import type { IStep } from '@/interfaces/step.interface'
import store from '@/store'
import {
  NEXT_STEP,
  PREV_STEP,
  SET_STEP,
  SHOPPING,
  STEP_COMPLETED,
  SAVE_FREEZE_CART
} from '@/store/modules/shopping/constants/store.constants'
import router from '@/router'

const stepperService = {
  getNextStep: (step?: number): IStep | undefined => {
    const curr: number = store.getters[`${SHOPPING}/getCurrentStep`]
    const steps: IStep[] = store.getters[`${SHOPPING}/getSteps`]
    const stepNumber: number = step ? step : curr
    let nextStep: IStep | undefined = steps.find(
      (step: IStep) => step.label === stepNumber.toString()
    )
    if (!nextStep && steps.length < stepNumber) {
      nextStep = {
        label: '4',
        description: 'resum-data.title',
        route: 'resum-data',
        name: 'resum-data'
      }
    }
    return nextStep
  },
  getPrevStep: (step?: number): IStep | undefined => {
    const curr: number = store.getters[`${SHOPPING}/getCurrentStep`]
    const steps: IStep[] = store.getters[`${SHOPPING}/getSteps`]
    const stepNumber: number = step ? step : curr
    const prevStep: IStep | undefined = steps.find(
      (step: IStep) => step.label === stepNumber.toString()
    )
    return prevStep
  },
  nextStep: (): IStep | undefined => {
    store.dispatch(`${SHOPPING}/${NEXT_STEP}`)
    //FreezeCart
    store.dispatch(`${SHOPPING}/${SAVE_FREEZE_CART}`)
    const nextStep: IStep | undefined = stepperService.getNextStep()
    if (nextStep?.route) {
      router.push({ path: nextStep.route })
      return nextStep
    }
  },
  prevStep(): void {
    store.dispatch(`${SHOPPING}/${PREV_STEP}`)
    const nextStep: IStep | undefined = stepperService.getPrevStep()
    if (nextStep?.route) {
      router.push({ path: nextStep.route })
    }
  },
  setCompletedStep(currentStep: number): void {
    const maxStepCompleted = store.getters[`${SHOPPING}/getStepCompleted`]

    if (currentStep > maxStepCompleted) {
      store.dispatch(`${SHOPPING}/${STEP_COMPLETED}`, currentStep)
    }
  },
  gotToStep(step: IStep): void {
    if (step?.label) {
      store.dispatch(`${SHOPPING}/${SET_STEP}`, parseInt(step.label))
    }
    if (step?.route) {
      router.push({ path: step.route })
    }
  }
}

export default stepperService
