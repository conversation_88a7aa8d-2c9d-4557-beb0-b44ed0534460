import type { IGdprRegistry } from '@/services/users/interfaces'
import type { IAdvertisingConfiguration } from '@/services/users/interfaces/advertising-configuration.interface'

export interface ILeadCandidate {
  position: string | null | undefined
  accountId?: string | null | undefined
  firstName?: string | null | undefined
  lastName1?: string | null | undefined
  lastName2?: string | null | undefined
  customDiscount?: string | null | undefined
  phone?: string | null | undefined
  email?: string | null | undefined
  shippingState?: string | null | undefined
  shippingStreet?: string | null | undefined
  shippingPostalCode?: string | null | undefined
  shippingCountry?: string | null | undefined
  shippingCity?: string | null | undefined
  productCity?: string | null | undefined
  productId?: string | null
  description?: string | null
  fiscalIdNumber?: string | null | undefined
  freelance?: string | null | undefined
  birthdate?: string | null | undefined
  gender?: string | null | undefined
  billingCountry?: string | null | undefined
  billingState?: string | null | undefined
  billingStreet?: string | null | undefined
  billingPostalCode?: string | null | undefined
  billingCity?: string | null | undefined
  paperBilling?: string | null | undefined
  advertisingConfigurations?: IAdvertisingConfiguration[] | null
  gdprRegistries?: IGdprRegistry[] | null
}
