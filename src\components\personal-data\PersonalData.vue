<template id="personal-data">
  <div :key="componentKey">
    <PwSeparator :title="t('personal-data.title')"></PwSeparator>
    <p class="p-4">
      {{ t('personal-data.description') }}
    </p>
    <PwRadio
      v-if="channel === channelWeb && !oldUser"
      class="my-6"
      @change="client"
      :options="options"
      :inline="true"
    ></PwRadio>
    <div v-if="isNewUser" class="p-4">
      <div v-if="loading" class="flex justify-center">
        <font-awesome-icon icon="fa-solid fa-spinner" class="max-w-[20px] text-info" />
      </div>
      <div class="" v-else>
        <PwRadio
          class="mb-4"
          :options="clientTypeOptions"
          :inline="true"
          @change="onClientTypeChange"
        ></PwRadio>
        <ul class="flex flex-wrap -mx-2">
          <li
            v-for="field of selectedFormFields"
            :key="field"
            :class="`w-${field.width} px-2 mt-2 relative`"
          >
            <div
              v-if="field.checkbox"
              class="absolute z-10 inset-y-0 right-0 flex items-start pr-3"
            >
              <PwCheck
                :lang="lang"
                class="h-4 w-4"
                name="customerType"
                ref="customerTypeRef"
                labelClass="text-gray text-sm"
                :label="t('personal-data.customer-type')"
                @change="changeCustomerType"
              />
            </div>
            <PwSelectAutocomplete
              v-if="field.type === 'selector'"
              :ref="allRefs[`${field.name}`]"
              type="selector"
              v-model="country"
              :value="country?.label"
              item-title="name"
              item-value="value"
              :lang="lang"
              :label="t('personal-data.' + field.label)"
              :placeholder="t('personal-data.' + field.label)"
              :items="nationalityOptions()"
              :customLabelClass="'!mb-2'"
              :customInputClass="'!p-2 !pl-3'"
              :inputError="field.error"
              :validations="field.validations || []"
              class="mt-4"
              classSelectorHeight="max-h-36"
              :disabled="field.disabled"
              @errors="(errors:any[]) => getErrors(errors, allRefs[`${field.name}`])"
            />
            <PwInputText
              v-else-if="field.type !== 'hidden'"
              :ref="allRefs[`${field.name}`]"
              :type="field.type"
              :lang="lang"
              v-model="field.value"
              :value="field.value"
              :label="t('personal-data.' + field.label)"
              :placeholder="t('personal-data.' + field.label)"
              :customLabelClass="field.disabled ? '!text-gray !mb-2' : '!mb-2'"
              :customInputClass="'!p-2 !pl-3 !mb-2'"
              :inputError="field.error"
              :validations="field.validations || []"
              class="mt-4"
              :disabled="field.disabled"
              @errors="(errors:any[]) => getErrors(errors, allRefs[`${field.name}`])"
            />
          </li>
        </ul>

        <div class="grid md:grid-cols-1 mb-6 mt-4">
          <pw-acceptance-legal-bases
            ref="acceptanceLegalBasesRef"
            :config="
              JSON.stringify({
                id: acceptanceLegalBasesId,
                customerId: oldUser?.id,
                companyName: companyName,
                customerData: acceptanceLegalBasesCustomerDataProperty(currentUser),
                lang: lang,
                requiredPrivacyPolicy: true,
                requiredNonCommercialCommunications: true,
                requiredCommercialCommunications: false,
                requiredThirdPartyCommercialCommunications: false,
                defaultPrivacyPolicyValue: isLegalBaseChecked('privacy-policy'),
                defaultNonCommercialCommunicationsValue: isLegalBaseChecked(
                  'non-commercial-communications'
                ),
                defaultCommercialCommunicationsValue: isLegalBaseChecked(
                  'commercial-communications'
                ),
                defaultThirdPartyCommercialCommunicationsValue: isLegalBaseChecked(
                  'third-party-commercial-communications'
                )
              })
            "
          ></pw-acceptance-legal-bases>
        </div>
      </div>
      <div class="flex justify-between items-center">
        <PwButton
          v-if="currentStep > 1"
          @click="prevStep"
          theme="light"
          class="w-[60px] max-w-[60px] h-[40px] mr-2"
        >
          <font-awesome-icon icon="fa-solid fa-angle-left" class="max-w-[15px] text-gray" />
        </PwButton>

        <PwButton
          @click="saveUser"
          :text="t('actions.next')"
          :disabled="!(isValidFormData && hasAcceptedLegalBase)"
        ></PwButton>
      </div>
    </div>

    <div v-if="!isNewUser" class="p-4">
      <div class="grid gap-6 md:grid-cols-1 md:max-w-sm md:m-auto mb-6">
        <PwInputText
          :lang="lang"
          :disabled="loading"
          :required="true"
          @input="watchUsername"
          :label="t('personal-data.user')"
          :name="'user'"
          :placeholder="t('personal-data.user')"
        ></PwInputText>
        <PwInputText
          :lang="lang"
          :disabled="loading"
          :required="true"
          :type="'password'"
          @input="watchPassword"
          :label="t('personal-data.password')"
          :name="'password'"
          :placeholder="t('personal-data.password')"
        ></PwInputText>
        <PwButton :disabled="loading" @click="login" :text="t('actions.sign-in')"></PwButton>
        <div v-if="loginError" class="text-error">
          {{ loginError }}
        </div>
        <div v-if="loading" class="flex justify-center">
          <font-awesome-icon icon="fa-solid fa-spinner" class="max-w-[20px] text-info" />
        </div>

        <div class="flex justify-between items-center">
          <p class="text-sm">
            <a target="_blank" href="https://areaclient.parlem.com/restablir-contrasenya">
              {{ t('personal-data.reset-password') }}
            </a>
          </p>
          <p class="text-sm text-right">
            <a target="_blank" href="https://areaclient.parlem.com/alta">
              {{ t('personal-data.create-account') }}
            </a>
          </p>
        </div>
      </div>
    </div>

    <div class="input-errors" v-for="error of v$.$errors" :key="error.$uid">
      <div class="text-sm text-error">{{ t('errors.' + error.$validator) }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import en from '@/i18n/locales/en.json'
import es from '@/i18n/locales/es.json'
import ca from '@/i18n/locales/ca.json'
import gl from '@/i18n/locales/gl.json'
import { useI18n } from 'vue-i18n'
import { Store, useStore } from 'vuex'
import moment from 'moment'
import {
  PwButton,
  PwRadio,
  PwSeparator,
  PwInputText,
  PwSelectAutocomplete,
  PwCheck,
  type IRadio
} from 'parlem-webcomponents-common'
import { useVuelidate } from '@vuelidate/core'
import { CHANNEL_CALL_CENTER, CHANNEL_WEB } from '@/services/constants/services.constants'
import {
  computed,
  ref,
  type ComputedRef,
  type Ref,
  reactive,
  toRefs,
  watch,
  onBeforeMount
} from 'vue'
import {
  SET_OLD_USER,
  UPDATE_NEW_USER,
  GET_PICKLISTS,
  SHOPPING,
  SET_GDPR,
  SET_ADV,
  GPDR_PRIVACY_POLICY_ID,
  GPDR_COMMERCIAL_COMMUNICATIONS_ID,
  GPDR_NON_COMMERCIAL_COMMUNICATIONS_ID,
  GPDR_THIRD_PARTY_COMMERCIAL_COMMUNICATIONS_ID,
  GET_ACCOUNT_ID,
  SET_USER_ID
} from '@/store/modules/shopping/constants/store.constants'
import type { IUserForm } from './interfaces/user-form.interface'
import usersService from '@/services/users/userService'
import type { IAuth, IContact, IGdprRegistry, IUser } from '@/services/users/interfaces'
import type { IPickListValue } from '@/services/api/crm/interfaces'
import type { ILeadCandidate } from '@/interfaces/lead-candidate'
import { leadsService, stepperService } from '@/services'
import type { IAdvertisingConfiguration } from '@/services/users/interfaces/advertising-configuration.interface'
import type { IStep } from '@/interfaces/step.interface'
import { useRouter } from 'vue-router'
import { formFieldsInputsParticular, formFieldsInputsBusiness } from '../../utils/formInputsBase'

export default {
  name: 'personal-data',
  components: {
    PwInputText,
    PwSeparator,
    PwButton,
    PwRadio,
    PwSelectAutocomplete,
    PwCheck
  },
  setup(props: any) {
    const store: Store<any> = useStore()
    const router = useRouter()
    let allRefs: any = {}

    const formFieldsParticular = ref(formFieldsInputsParticular)
    const formFieldsBusiness = ref(formFieldsInputsBusiness)

    let country: Ref<Partial<IPickListValue>> = ref({})
    const countries: Ref<IPickListValue[]> = reactive(store.getters[`${SHOPPING}/getCountries`])

    const lang: Ref<string> = computed(() => store.getters[`${SHOPPING}/getLang`])

    let acceptanceLegalBasesRef = ref<any>(null)
    const currentStep: number = store.getters[`${SHOPPING}/getCurrentStep`]
    let currentUser = ref<any>()
    const channelCallCenter = ref<any>(CHANNEL_CALL_CENTER)
    const channelWeb = ref<any>(CHANNEL_WEB)
    const componentKey = ref(0)

    const { t, locale }: any = useI18n({
      messages: {
        en,
        gl,
        es,
        ca
      }
    })
    locale.value = lang.value || locale.value
    const minDate: string = moment().subtract(150, 'years').format('YYYY-MM-DD')
    const maxDate: string = moment().subtract(18, 'years').format('YYYY-MM-DD')

    let loading: Ref<boolean> = ref<boolean>(false)

    const v$ = useVuelidate()
    const event: { isNewUser: boolean; username: string; password: string; loginError: string } =
      reactive({
        username: '',
        password: '',
        isNewUser: true,
        loginError: ''
      })
    const newCustomer: ComputedRef<string> = computed(() => t('personal-data.new-customer'))
    const oldCustomer: ComputedRef<string> = computed(() => t('personal-data.old-customer'))
    const company: Ref<string> = computed(() => store.getters[`${SHOPPING}/getCompany`])

    const companyName: ComputedRef<string> = computed(() => company.value)
    let options: Ref<IRadio[]> = ref([
      {
        label: newCustomer,
        value: 'new',
        checked: true,
        disabled: false
      },
      {
        label: oldCustomer,
        value: 'old',
        checked: false,
        disabled: false
      }
    ])

    const clientTypeOptions = ref([
      {
        label: t('personal-data.particular'),
        value: 'particular',
        checked: true,
        disabled: false
      },
      {
        label: t('personal-data.business'),
        value: 'business',
        checked: false,
        disabled: false
      }
    ])

    let isValidFormData: Ref<boolean> = ref(false)
    let hasAcceptedLegalBase: Ref<boolean> = ref(false)

    let acceptedLegalBaseInputs: IAdvertisingConfiguration[]
    let acceptedGdprInputs: IGdprRegistry

    const selectedFormFields: Ref<any> = ref([])
    const acceptanceLegalBasesId: string = 'personal-data-acceptance-legal-bases'

    let oldUser: ComputedRef<IUser> = computed(() => store.getters[`${SHOPPING}/getOldUser`])
    let newUser: ComputedRef<IUser> = computed(() => store.getters[`${SHOPPING}/getNewUser`])
    const channel: ComputedRef<IUser> = computed(() => store.getters[`${SHOPPING}/getChannel`])
    currentUser.value = oldUser.value

    onBeforeMount(() => {
      setDataFromStore()
      if (newUser.value && newUser.value.personalData && newUser.value.personalData.documentType) {
        if (newUser.value.personalData.documentType === 'CIF') {
          setClientType('business')
        } else {
          setClientType('particular')
        }
      }
      setSelectedFormFields()
      loadLegalWebComponent()
    })

    const isNIE = (documentNumber: any): boolean => {
      if (documentNumber) {
        const nieRegex = /^[XYZxyz]\d{7}[A-Za-z]$/
        return nieRegex.test(
          typeof documentNumber === 'string' ? documentNumber : documentNumber.value
        )
      }
      return false
    }

    const isDNI = (documentNumber: any): boolean => {
      if (documentNumber) {
        const dniRegex = /^\d{8}[A-Za-z]$/
        return dniRegex.test(
          typeof documentNumber === 'string' ? documentNumber : documentNumber.value
        )
      }
      return false
    }

    const isCIF = (documentNumber: any): boolean => {
      if (documentNumber) {
        const dniRegex = /^[ABCDEFGHJKLMNPQRSUVW]\d{7}[0-9A-J]$/
        return dniRegex.test(
          typeof documentNumber === 'string' ? documentNumber : documentNumber.value
        )
      }
      return false
    }

    function setSelectedFormFields(): void {
      const isBusinessChecked = clientTypeOptions.value.some(
        (option) => option.value === 'business' && option.checked
      )

      selectedFormFields.value = isBusinessChecked
        ? formFieldsBusiness.value
        : formFieldsParticular.value
      if (isBusinessChecked) {
        const nationalityField = selectedFormFields.value.find(
          (field: any) => field.name === 'nationality'
        )
        if (nationalityField) {
          nationalityField.value = 'ES'
          country.value = { value: 'ES', label: 'Espanya' }
        }
      }

      selectedFormFields.value.forEach((field: any) => {
        allRefs[field.name] = ref<{ [key: string]: any } | null>(null)
      })
    }

    const setClientType = (type: any) => {
      clientTypeOptions.value.forEach((option) => {
        option.checked = option.value === type
      })
    }

    const onClientTypeChange = (newType: any) => {
      setClientType(newType.value)
      clearFields()
    }
    const nationalityOptions = () => {
      const allCountries = store.getters[`${SHOPPING}/getCountries`]
      let sortedCountries = allCountries.map((country: any) => ({
        ...country,
        name: country.name || country.label,
        value: country.value || country.code
      }))
      const spainIndex = sortedCountries.findIndex((country: any) => country.value === 'ES')
      if (spainIndex > -1) {
        const [spain] = sortedCountries.splice(spainIndex, 1)
        sortedCountries.unshift(spain)
      }
      if (isNIE(documentNumberField.value)) {
        const currentOptions = sortedCountries.filter((option: any) => option.value !== 'ES')
        sortedCountries = currentOptions
      } else if (isDNI(documentNumberField.value) || isCIF(cifDocumentNumberField.value)) {
        const currentOptions = sortedCountries.filter((option: any) => option.value === 'ES')
        sortedCountries = currentOptions
      }
      return sortedCountries
    }

    function prevStep(): void {
      stepperService.prevStep()
    }

    function isLegalBaseChecked(legalBaseName: any): boolean {
      if (!currentUser.value) {
        return false
      }

      if (legalBaseName === 'privacy-policy') {
        return (
          !!currentUser.value.gdprRegistries.find(
            (d: any) => d.messageType === GPDR_PRIVACY_POLICY_ID && d.accepted
          ) || !!currentUser.value.gdprRegistries[0].accepted
        )
      }
      if (legalBaseName === 'commercial-communications') {
        return !!currentUser.value.advertisingConfigurations.find(
          (d: any) => d.messageType === GPDR_COMMERCIAL_COMMUNICATIONS_ID && d.accepted
        )
      }
      if (legalBaseName === 'non-commercial-communications') {
        return !!currentUser.value.advertisingConfigurations.find(
          (d: any) => d.messageType === GPDR_NON_COMMERCIAL_COMMUNICATIONS_ID && d.accepted
        )
      }
      if (legalBaseName === 'third-party-commercial-communications') {
        return !!currentUser.value.advertisingConfigurations.find(
          (d: any) => d.messageType === GPDR_THIRD_PARTY_COMMERCIAL_COMMUNICATIONS_ID && d.accepted
        )
      }

      return false
    }

    function acceptanceLegalBasesCustomerDataProperty(user: IUser): string | undefined {
      if (user) {
        return encodeURIComponent(JSON.stringify(user))
      }
      return undefined
    }

    function loadLegalWebComponent(): void {
      const existScript: HTMLElement | null = document.getElementById('wc-legal')
      if (!existScript) {
        const legalScript: HTMLScriptElement = document.createElement('script')
        legalScript.setAttribute('src', import.meta.env.VITE_LEGAL_WC)
        legalScript.setAttribute('id', 'wc-legal')
        legalScript.async = true
        document.head.appendChild(legalScript)
      }
    }

    window.addEventListener('change-acceptance-legal-bases', (e: any) => {
      const data = e.detail
      if (data && data.id === acceptanceLegalBasesId) {
        hasAcceptedLegalBase.value = data.isAllRequiredChecked
        acceptedLegalBaseInputs = data.advertisingConfigurations
        acceptedGdprInputs = data.gdprRegistry
      }
    })

    watch(country, (newCountryValue: Partial<IPickListValue> | null) => {
      if (newCountryValue && newCountryValue?.value) {
        const nationalityField = selectedFormFields.value.find(
          (field: any) => field.name === 'nationality'
        )
        if (nationalityField) {
          nationalityField.value = newCountryValue.value
        }
      }
    })
    function getErrors(errors: any[], ref: any) {
      const emailField = selectedFormFields.value.find((field: any) => field.name === 'email')
      const value = emailField?.value || ''

      if (emailField && ref === allRefs['email']) {
        if (isValidEmailFormat(value)) {
          ref.errors = []
        } else {
          ref.errors = errors
        }
      } else {
        ref.errors = errors
      }

      checkIsValidForm(selectedFormFields.value)
    }

    function isValidEmailFormat(email: string): boolean {
      const emailRegex = /^[a-zA-Z0-9._%+-]+(?<!\.)@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$/
      return emailRegex.test(email)
    }

    function checkIsValidForm(newFormData: IUserForm): void {
      let isValidForm = true
      const allRefsArray: { [key: string]: any }[] = Object.values(allRefs)
      const nationalityField = selectedFormFields.value.find(
        (field: any) => field.name === 'nationality'
      )
      if (allRefsArray.some((inputRef: any) => inputRef.errors?.length)) {
        isValidForm = false
      }
      if (
        ((isDNI(documentNumberField.value) || isCIF(documentNumberField.value)) &&
          nationalityField.value !== 'ES') ||
        (isNIE(documentNumberField.value) && nationalityField.value === 'ES')
      ) {
        isValidForm = false
      }
      for (const keyForm of Object.keys(newFormData) as (keyof IUserForm)[]) {
        const field = newFormData[keyForm]
        if (field?.value === null || field?.value === '' || field?.value === undefined) {
          isValidForm = false
          break
        }
      }
      isValidFormData.value = isValidForm
    }

    watch(
      selectedFormFields,
      (newFormData) => {
        checkIsValidForm(newFormData)
      },
      { deep: true }
    )

    function receiveEmail(value: any): void {
      const emailField = selectedFormFields.value.find((field: any) => field.name === 'email')
      if (emailField) {
        emailField.value = value
      }
    }

    function client(option: IRadio): void {
      event.isNewUser = option?.value === 'old' ? false : true
    }

    function disableInputsOldUser(): void {
      const firstNameField = selectedFormFields.value.find(
        (field: any) => field.name === 'firstName'
      )
      const lastNameField = selectedFormFields.value.find((field: any) => field.name === 'lastName')
      const nationalityField = selectedFormFields.value.find(
        (field: any) => field.name === 'nationality'
      )
      const birthdateField = selectedFormFields.value.find(
        (field: any) => field.name === 'birthdate'
      )
      const documentNumberField = selectedFormFields.value.find(
        (field: any) => field.name === 'documentNumber'
      )

      if (firstNameField) firstNameField.disabled = true
      if (lastNameField) lastNameField.disabled = true

      if (birthdateField && birthdateField.value) {
        const birthdate = new Date(birthdateField.value)
        if (!isNaN(birthdate.getTime())) {
          birthdateField.disabled = birthdate >= new Date('1901-01-01')
        }
      }
      if (
        nationalityField &&
        nationalityField.value !== null &&
        nationalityField.value !== '' &&
        country.value !== null &&
        country.value?.value !== '' &&
        country.value?.value !== null
      ) {
        if (documentNumberField && documentNumberField.value) {
          if (isNIE(documentNumberField.value)) {
            nationalityField.disabled = nationalityField.value !== 'ES' ? true : false
          } else if (
            (isDNI(documentNumberField.value) || isCIF(documentNumberField.value)) &&
            nationalityField.value !== 'ES'
          ) {
            nationalityField.disabled = false
          } else {
            nationalityField.disabled = true
          }
        }
      }
    }

    function setData(user: any) {
      if (user) {
        const contact: IContact | undefined = user?.provisionContacts.find(
          (contact: IContact) => contact.isDefault
        )
        const completeNameField = selectedFormFields.value.find(
          (field: any) => field.name === 'completeName'
        )
        const firstNameField = selectedFormFields.value.find(
          (field: any) => field.name === 'firstName'
        )
        const lastNameField = selectedFormFields.value.find(
          (field: any) => field.name === 'lastName'
        )
        const documentNumberField = selectedFormFields.value.find(
          (field: any) => field.name === 'documentNumber'
        )
        const birthdateField = selectedFormFields.value.find(
          (field: any) => field.name === 'birthdate'
        )
        const foundationDateField = selectedFormFields.value.find(
          (field: any) => field.name === 'foundationDate'
        )
        const emailField = selectedFormFields.value.find((field: any) => field.name === 'email')
        const phoneField = selectedFormFields.value.find((field: any) => field.name === 'phone')
        const nationalityField = selectedFormFields.value.find(
          (field: any) => field.name === 'nationality'
        )
        const customerTypeField = selectedFormFields.value.find(
          (field: any) => field.name === 'customerType'
        )
        const administratorDocumentNumberField = selectedFormFields.value.find(
          (field: any) => field.name === 'administratorDocumentNumber'
        )
        const administratorDocumentTypeField = selectedFormFields.value.find(
          (field: any) => field.name === 'administratorDocumentType'
        )
        const companyManagerField = selectedFormFields.value.find(
          (field: any) => field.name === 'companyManager'
        )
        const companyManagerFirstNameField = selectedFormFields.value.find(
          (field: any) => field.name === 'companyManagerFirstName'
        )
        const companyManagerLastNameField = selectedFormFields.value.find(
          (field: any) => field.name === 'companyManagerLastName'
        )

        if (completeNameField) completeNameField.value = user?.personalData?.completeName
        if (firstNameField) firstNameField.value = user?.personalData?.firstName
        if (lastNameField) lastNameField.value = user?.personalData?.lastName
        if (documentNumberField) documentNumberField.value = user?.personalData?.documentNumber
        if (customerTypeField)
          customerTypeField.value = user?.personalData?.customerType || 'CustomerResidential'
        if (birthdateField && new Date(birthdateField.value) >= new Date('1901-01-01')) {
          birthdateField.value = moment(user?.personalData?.personBirthdate).format('YYYY-MM-DD')
        }
        if (emailField) emailField.value = contact?.email
        if (foundationDateField)
          foundationDateField.value = moment(user?.personalData?.foundationDate).format(
            'YYYY-MM-DD'
          )
        if (emailField) emailField.value = contact?.email
        if (phoneField) phoneField.value = contact?.phone
        if (
          nationalityField &&
          (((isDNI(user?.personalData?.documentNumber) ||
            isCIF(user?.personalData?.documentNumber)) &&
            user?.personalData?.nationality === 'ES') ||
            (isNIE(user?.personalData?.documentNumber) && user?.personalData?.nationality !== 'ES'))
        ) {
          nationalityField.value = user?.personalData?.nationality
        }

        if (administratorDocumentNumberField)
          administratorDocumentNumberField.value =
            user?.companyStructure?.administratorDocumentNumber

        if (administratorDocumentTypeField)
          administratorDocumentTypeField.value = setDocumentType(
            user?.companyStructure?.administratorDocumentNumber
          )
        if (companyManagerFirstNameField)
          companyManagerFirstNameField.value =
            user?.companyStructure?.companyManager ||
            user?.companyStructure?.companyManagerFirstName ||
            ''

        if (companyManagerLastNameField)
          companyManagerLastNameField.value = user?.companyStructure?.companyManagerLastName || ''
        if (
          (isDNI(user?.personalData?.documentNumber) && user?.personalData?.nationality === 'ES') ||
          (isNIE(user?.personalData?.documentNumber) && user?.personalData?.nationality !== 'ES')
        ) {
          country.value = store.getters[`${SHOPPING}/getCountries`]?.find(
            (country: IPickListValue) => country.value === user?.personalData?.nationality
          )
        }

        if (user?.id) {
          disableInputsOldUser()
          hasAcceptedLegalBase.value = true
          options.value = options.value.map((option: IRadio) => {
            option.checked = option.value === 'old'
            option.disabled = true
            return option
          })
          checkIsValidForm(selectedFormFields.value)
        }
      }
    }
    async function setDataFromStore(): Promise<void> {
      loading.value = true
      const newUser: IUser = store.getters[`${SHOPPING}/getNewUser`]
      const oldUser: IUser = store.getters[`${SHOPPING}/getOldUser`]

      await store.dispatch(`${SHOPPING}/${GET_PICKLISTS}`)

      const user =
        oldUser?.personalData?.firstName || oldUser?.personalData?.documentType === 'CIF'
          ? oldUser
          : newUser?.personalData?.firstName || newUser?.personalData?.documentType === 'CIF'
          ? newUser
          : null

      setData(user)
      currentUser.value = oldUser ? oldUser : newUser
      loading.value = false
    }
    function setDataFromLogin(user: IUser): void {
      if (!user) {
        return
      }
      store.dispatch(`${SHOPPING}/${SET_OLD_USER}`, user)
      const oldUser: IUser = store.getters[`${SHOPPING}/getOldUser`]

      event.isNewUser = true
      setData(oldUser)
    }
    async function login(): Promise<void> {
      try {
        loading.value = true
        const auth: IAuth = await usersService.login(event.username, event.password)

        if (auth) {
          const userData: IUser = await usersService.get(auth.user.crmId, auth.user.companyName)
          if (userData) {
            setDataFromLogin(userData)
          }
          loading.value = false
        }
      } catch (error) {
        loading.value = false
        const message: ComputedRef<string> = computed(() => t('personal-data.login-error'))
        event.loginError = message.value
        error
      }
    }

    function watchUsername(e: any): void {
      event.username = e.target.value
    }

    function watchPassword(e: any): void {
      event.password = e.target.value
    }

    function setDocumentType(documentNumber: string): string {
      return isDNI(documentNumber)
        ? 'DNI'
        : isCIF(documentNumber)
        ? 'CIF'
        : isNIE(documentNumber)
        ? 'NIE'
        : 'No valid'
    }

    function saveUser(): void {
      store.dispatch(`${SHOPPING}/${UPDATE_NEW_USER}`, selectedFormFields.value)
      createLeadCandidate()
      const currentStep: number = store.getters[`${SHOPPING}/getCurrentStep`]
      setCompletedStep(currentStep)
      nextStep()
    }

    function nextStep(): void {
      const nextStep: IStep | undefined = stepperService.nextStep()
      if (nextStep?.route) {
        router.push({ path: nextStep.route })
      }
    }

    function setCompletedStep(currentStep: number): void {
      stepperService.setCompletedStep(currentStep)
    }

    async function createLeadCandidate(): Promise<void> {
      const leadCandidate: ILeadCandidate = getLeadCandidateData()
      store.dispatch(`${SHOPPING}/${SET_GDPR}`, leadCandidate.gdprRegistries)
      store.dispatch(`${SHOPPING}/${SET_ADV}`, leadCandidate.advertisingConfigurations)
      const newLeadCandidate: ILeadCandidate = await leadsService.createLeadCandidate(leadCandidate)
    }

    function changeCustomerType() {
      const customerTypeField = formFieldsParticular.value.find(
        (field: any) => field.name === 'customerType'
      )
      if (customerTypeField) {
        customerTypeField.value =
          customerTypeField.value === 'CustomerResidential'
            ? 'CustomerFreelance'
            : 'CustomerResidential'
      }
    }

    function getLeadCandidateData(): ILeadCandidate {
      const newUser = store.getters[`${SHOPPING}/getNewUser`]
      const userDefaultContact = store.getters[`${SHOPPING}/getUserDefaultContact`]
      const step: number = store.getters[`${SHOPPING}/getCurrentStep`]
      const steps: IStep[] = store.getters[`${SHOPPING}/getSteps`]
      const stepData: IStep | undefined = steps.find((st: IStep) => st.label === step.toString())

      const position: string =
        channel.value !== channelWeb.value
          ? 'Confirmed'
          : stepData
          ? `${stepData.label} - ${stepData.name}`
          : `${step}`

      const accountId: string = store.getters[`${SHOPPING}/${GET_ACCOUNT_ID}`]

      let lead: ILeadCandidate = {
        phone: userDefaultContact?.phone,
        email: userDefaultContact?.email,
        firstName: newUser.personalData.firstName,
        lastName1: newUser.personalData.lastName,
        fiscalIdNumber: newUser.personalData.documentNumber,
        birthdate: newUser.personalData.personBirthdate,
        position
      }

      if (accountId) {
        lead = { ...lead, accountId }
      }
      lead = {
        ...lead,
        gdprRegistries: [acceptedGdprInputs],
        advertisingConfigurations: acceptedLegalBaseInputs
      }

      return lead
    }

    const documentRegex =
      /^(?:\d{8}[TRWAGMYFPDXBNJZSQVHLCKE]|[XYZ]\d{7}[TRWAGMYFPDXBNJZSQVHLCKE]|[ABCDEFGHJKLMNPQRSUVW]\d{7}[0-9A-J])$/

    const cifRegex = /^[ABCDEFGHJKLMNPQRSUVW][0-9]{7}[0-9A-J]$/

    const documentNumberField = computed(() => {
      const field = formFieldsParticular.value.find((field: any) => field.name === 'documentNumber')
      return field
    })

    const cifDocumentNumberField = computed(() => {
      const field = formFieldsBusiness.value.find((field: any) => field.name === 'documentNumber')
      return field
    })

    const administratorDocumentNumberField = computed(() => {
      const field = formFieldsBusiness.value.find(
        (field: any) => field.name === 'administratorDocumentNumber'
      )
      return field
    })

    let isValidDocumentNumber = true

    async function setOldUser(userId: any, type: 'DNI' | 'CIF') {
      isValidDocumentNumber = true

      if (
        (type === 'DNI' && documentRegex.test(userId)) ||
        (type === 'CIF' && cifRegex.test(userId))
      ) {
        await store.dispatch(`${SHOPPING}/${SET_USER_ID}`, userId)
      } else {
        store.dispatch(`${SHOPPING}/${SET_OLD_USER}`, null)
      }

      if (userId === '' || userId === null || userId === undefined) {
        clearFields()
      }

      if (allRefs?.documentNumber?.value?.[0]?.$v && allRefs?.documentNumber?.value?.[0]?.$v()) {
        isValidDocumentNumber = false
      }
    }

    watch(
      () => documentNumberField.value?.value,
      (newDocumentNumber) => {
        if (newDocumentNumber) {
          nationalityOptions()
        }
      }
    )
    watch(
      () => cifDocumentNumberField.value?.value,
      (newDocumentNumber) => {
        if (newDocumentNumber) {
          nationalityOptions()
        }
      }
    )

    watch(
      () => (documentNumberField.value ? documentNumberField.value.value : null),
      async (userId) => {
        await setOldUser(userId, 'DNI')
      }
    )

    watch(
      () => (cifDocumentNumberField.value ? cifDocumentNumberField.value.value : null),
      async (userId) => {
        await setOldUser(userId, 'CIF')
      }
    )

    watch(
      () =>
        administratorDocumentNumberField.value
          ? administratorDocumentNumberField.value.value
          : null,
      async (documentNumber) => {
        const administratorDocumentType = selectedFormFields.value.find(
          (field: any) => field.name === 'administratorDocumentType'
        )
        administratorDocumentType.value = setDocumentType(documentNumber)
      }
    )

    watch(oldUser, (updatedUser) => {
      if (updatedUser) {
        setDataFromStore()
      } else {
        isLegalBaseChecked('privacy-policy')
        isLegalBaseChecked('commercial-communications')
        isLegalBaseChecked('non-commercial-communications')
        isLegalBaseChecked('third-party-commercial-communications')
      }
    })

    function clearFields() {
      if (newUser.value) {
        newUser.value.gdprRegistries.forEach((item: any) => {
          item.accepted = false
        })
        newUser.value.advertisingConfigurations.forEach((item: any) => {
          item.accepted = false
        })
      }

      if (oldUser.value) {
        oldUser.value.gdprRegistries.forEach((item: any) => {
          item.accepted = false
        })
        oldUser.value.advertisingConfigurations.forEach((item: any) => {
          item.accepted = false
        })
      }

      store.dispatch(`${SHOPPING}/${SET_OLD_USER}`, null)
      setSelectedFormFields()

      selectedFormFields.value.forEach((field: any) => {
        field.value = ''
        field.disabled = false
      })
      country.value = { value: '', label: '' }
      const customerTypeField = selectedFormFields.value.find(
        (field: any) => field.name === 'customerType'
      )
      if (customerTypeField) customerTypeField.value = 'CustomerResidential'
      hasAcceptedLegalBase.value = false
      isValidDocumentNumber = false
      selectedFormFields.value.forEach((field: any) => {
        allRefs[field.name] = ref<{ [key: string]: any } | null>(null)
      })

      componentKey.value += 1
    }

    return {
      t,
      v$,
      options,
      client,
      receiveEmail,
      watchUsername,
      watchPassword,
      login,
      ...toRefs(event),
      store,
      saveUser,
      loading,
      minDate,
      maxDate,
      country,
      countries,
      isValidFormData,
      hasAcceptedLegalBase,
      acceptanceLegalBasesId,
      acceptanceLegalBasesRef,
      oldUser,
      newUser,
      currentUser,
      acceptanceLegalBasesCustomerDataProperty,
      isLegalBaseChecked,
      companyName,
      lang,
      channel,
      channelCallCenter,
      channelWeb,
      nationalityOptions,
      changeCustomerType,
      currentStep,
      prevStep,
      formFieldsParticular,
      formFieldsBusiness,
      clientTypeOptions,
      selectedFormFields,
      onClientTypeChange,
      componentKey,
      allRefs,
      documentNumberField,
      isValidDocumentNumber,
      getErrors
    }
  }
}
</script>
