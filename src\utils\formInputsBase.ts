export const formFieldsInputsBusiness: any = [
  // PENDING
  {
    name: 'documentNumber',
    label: 'cif-number',
    type: 'text',
    value: '',
    required: true,
    validations: ['required', 'cif'],
    disabled: false,
    width: '1/2'
  },
  {
    name: 'completeName',
    label: 'business-name',
    type: 'text',
    value: '',
    required: true,
    validations: ['required'],
    disabled: false,
    width: '1/2'
  },
  {
    name: 'companyManagerFirstName',
    label: 'admin-name',
    type: 'text',
    value: '',
    required: true,
    validations: ['required'],
    disabled: false,
    width: '1/2'
  },
  {
    name: 'companyManagerLastName',
    label: 'admin-surname',
    type: 'text',
    value: '',
    required: true,
    validations: ['required'],
    disabled: false,
    width: '1/2'
  },
  {
    name: 'administratorDocumentNumber',
    label: 'admin-id-number',
    type: 'text',
    value: '',
    required: true,
    validations: ['required', 'dni'],
    disabled: false,
    width: '1/2'
  },

  {
    name: 'foundationDate',
    label: 'foundation-date',
    type: 'date',
    value: '',
    required: true,
    validations: [
      'required',
      `maxDate:${
        new Date(new Date().setFullYear(new Date().getFullYear())).toISOString().split('T')[0]
      }`,
      'minDate:1200-01-01'
    ],
    disabled: false,
    width: '1/2'
  },
  {
    name: 'nationality',
    label: 'nationality',
    type: 'selector',
    value: '',
    required: true,
    validations: ['required'],
    disabled: false,
    width: '1/2'
  },
  {
    name: 'phone',
    label: 'phone',
    type: 'tel',
    value: '',
    required: true,
    validations: ['required', 'phone'],
    disabled: false,
    width: '1/2'
  },
  {
    name: 'email',
    label: 'email',
    type: 'email',
    value: '',
    required: true,
    validations: ['required', 'email'],
    disabled: false,
    width: 'full'
  },
  {
    name: 'administratorDocumentType',
    label: 'admin-id-type-number',
    type: 'hidden',
    value: 'DNI',
    required: true,
    validations: ['required'],
    disabled: false,
    width: '1/2'
  }
]

export const formFieldsInputsParticular: any = [
  {
    name: 'firstName',
    type: 'text',
    value: null,
    label: 'name',
    placeholder: 'name',
    width: '1/2',
    validations: ['required'],
    disabled: false
  },
  {
    name: 'lastName',
    type: 'text',
    value: null,
    label: 'surname',
    placeholder: 'surname',
    width: '1/2',
    validations: ['required'],
    disabled: false
  },
  {
    name: 'phone',
    type: 'tel',
    value: null,
    label: 'phone',
    placeholder: 'phone',
    width: '1/2',
    validations: ['required', 'phone'],
    disabled: false
  },
  {
    name: 'documentNumber',
    type: 'text',
    value: null,
    label: 'id-number',
    placeholder: 'id-number',
    width: '1/2',
    checkbox: true,
    validations: ['dni'],
    disabled: false
  },
  {
    name: 'birthdate',
    type: 'date',
    value: null,
    label: 'birthdate',
    placeholder: 'birthdate',
    width: '1/2',
    validations: [
      'required',
      `maxDate:${
        new Date(new Date().setFullYear(new Date().getFullYear() - 18)).toISOString().split('T')[0]
      }`,
      'minDate:1901-01-01'
    ],
    disabled: false
  },
  {
    name: 'nationality',
    type: 'selector',
    value: null,
    options: {},
    label: 'nationality',
    placeholder: 'nationality',
    width: '1/2',
    validations: ['required'],
    disabled: false
  },
  {
    name: 'email',
    type: 'email',
    value: null,
    options: null,
    label: 'email',
    placeholder: 'email',
    width: 'full',
    validations: ['required', 'email'],
    disabled: false
  },
  {
    name: 'customerType',
    type: 'hidden',
    value: 'CustomerResidential',
    label: 'customer-type',
    placeholder: 'customer-type',
    width: '1/2',
    validations: ['required'],
    disabled: false
  }
]
