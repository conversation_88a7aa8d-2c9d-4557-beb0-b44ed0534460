export interface IAddressForm {
  type: any
  street: string
  number?: string
  stair?: string | null
  bis?: string
  gate?: string
  block?: string
  letter?: string
  floor?: string
  firstHand?: string
  secondHand?: string
  postalCode: string
  location?: string
  fullAddress?: string
  streetType?: string
  streetNumber?: string | null
  town?: string
  state?: string
  gescal17: string
  gescal37: string
  door?: string | number
  city?: string | null
  province?: string | null
  zip?: string | null
  isDefault?: boolean
  specification?: string | null
  ineCode?: string | null
}
