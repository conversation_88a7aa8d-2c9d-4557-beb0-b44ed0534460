# Node.js with Vue
# Build a Node.js project that uses Vue.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript
variables:
  azureSubscription: AzureSubscription
  version: v$(Build.BuildId)
  isMain: $[eq(variables['Build.SourceBranch'], 'refs/heads/master')]
trigger:
  - develop
  - master
pool:
  vmImage: ubuntu-latest
name: v$(Build.BuildId)
stages:
  - stage: Artifacts
    displayName: Build artifacts
    jobs:
      - job: 'BuildArtifact'
        steps:
          # npm
          - task: NodeTool@0
            inputs:
              versionSpec: '18.18.2'
            displayName: 'Build'
          - script: |
              npm install -g pnpm
              npx update-browserslist-db@latest
              npm install
              npm run build
            displayName: 'npm install and build'
          - task: CopyFiles@1
            displayName: 'Copy Files to: $(Build.ArtifactStagingDirectory)'
            inputs:
              SourceFolder: ./dist
              TargetFolder: '$(Build.ArtifactStagingDirectory)/shopping/dist'
            condition: and(succeeded(), eq(variables.isMain, true))
          - task: PublishPipelineArtifact@1
            displayName: 'Publish Aritifact'
            inputs:
              targetPath: $(Build.ArtifactStagingDirectory)/shopping/dist
              artifactName: pwshopping
            condition: and(succeeded(), eq(variables.isMain, true))
