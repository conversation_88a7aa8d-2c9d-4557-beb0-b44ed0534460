export interface IAddressForm {
  type?: any
  street?: string
  number?: string
  stair?: string | null
  letter?: string
  block?: string
  bis?: string
  gate?: string
  floor?: string
  secondHand?: string
  firstHand?: string
  postalCode?: string
  location?: string
  fullAddress?: string
  streetType?: string
  streetNumber?: string | null
  town?: string
  state?: string
  gescal17?: string
  door?: string | number
  city?: string | null
  province?: string | null
  zip?: string | null
  isDefault?: boolean
  specification?: string | null
  ineCode?: string | null
}
