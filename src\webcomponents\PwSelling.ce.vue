<template id="pw-shopping">
  <div class="@container">
    <section v-if="!loading && !showUserError">
      <!--       <Banner v-if="channel === CHANNEL_WEB" :lang="locale" />
 -->
      <PwStepper
        class="max-w-[700px] md:max-w-[950px] m-auto mb-4"
        :class="channel === CHANNEL_WEB && rate?.id ? '-mt-3 md:-mt-4' : 'pt-4'"
        ref="pwStepperRef"
        theme="primary"
        @stepClicked="goTostep"
        :initial-step="1"
        :currentstep="currentStep"
        :steps="steps"
      ></PwStepper>
      <div
        v-if="showNoAccountMessage"
        class="flex justify-center items-center text-white bg-error mb-2 p-1 font-bold"
      >
        <font-awesome-icon icon="fa-solid fa-triangle-exclamation" class="max-w-5 mr-2" />
        <p>
          {{ t('errors.account') }}
        </p>
      </div>
      <router-view />
    </section>
    <div v-if="loading" class="flex justify-center mt-10">
      <font-awesome-icon icon="fa-solid fa-spinner" class="max-w-[20px] text-info" />
    </div>
    <div v-if="showUserError" class="flex justify-center flex-col items-center m-10">
      <font-awesome-icon icon="fa-solid fa-triangle-exclamation" class="max-w-[80px] text-error" />
      <p class="mt-5 text-lg font-semibold text-error">{{ t('errors.general-error') }}</p>
      <p class="mt-3 text-lg font-semibold text-error">{{ t('errors.username') }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { watch } from 'vue'
import { PwStepper } from 'parlem-webcomponents-common'
import { changeColors } from 'parlem-webcomponents-common'
import Banner from '@/components/banner/Banner.vue'
import en from '@/i18n/locales/en.json'
import es from '@/i18n/locales/es.json'
import ca from '@/i18n/locales/ca.json'
import gl from '@/i18n/locales/gl.json'
import { type ComputedRef, computed, type Ref, ref, onBeforeMount, toRef } from 'vue'
import {
  SET_RATE_CODE,
  SET_LANG,
  SET_STEP,
  SET_STEPS,
  SHOPPING,
  SET_TRADEMARK,
  SET_PRODUCT_FAMILY,
  GET_RATE_BY_CODE,
  SET_GESCAL,
  SET_USER_ID,
  SET_OLD_USER,
  UPDATE_NEW_USER,
  GET_FREEZE_CART,
  GET_FREEZE_CART_WITH_OPPORTUNITY_ID,
  GET_USER_INFO,
  GET_USERNAME,
  INITIAL_STEPS,
  WEB_CHANNEL_STEPS,
  SAVE_OPPORTUNITY_ID,
  CLEAN_SELLING_DATA,
  GET_USER_ID,
  SET_USER_INFO,
  GET_SHOW_NO_ACCOUNT_MESSAGE,
  GET_COMPANY,
  GET_CHANNEL,
  GET_TRADEMARK
} from '@/store/modules/shopping/constants/store.constants'
import type { IStep } from '@/interfaces/step.interface'
import { useRouter } from 'vue-router'
import { stepperService } from '@/services'
import type { IRateDataParams } from '@/services/api/shoppingcart/interfaces/rate-data-params.interface'
import { CHANNEL_WEB } from '@/services/constants/services.constants'
import apiShoppingcartService from '../services/api/shoppingcart/apiShoppincartService'
import type { IInfoUser } from '../services/api/shoppingcart/interfaces/user-info.interface'
import type { IContact, IUser } from '@/services/users/interfaces'
import type { IUserForm } from '../components/personal-data/interfaces/user-form.interface'
import moment from 'moment'
import type { IRate } from '@/services/api/shoppingcart/interfaces/rate.interface'
import checkUserCredentials from '@/utils/userCredential'
import type { IUserInfo } from '@/services/users/interfaces/user-info.interface'
const props = defineProps({
  config: {
    type: String,
    default: "{ 'lang': 'ca' }"
  }
})
const configRef = toRef(props, 'config')
let configStr: any = JSON.parse(configRef.value || '{}')
const router = useRouter()
const store = useStore()
const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca
  }
})
locale.value = configStr.lang || locale.value

const loading: Ref<boolean> = ref(false)
const showUserError: Ref<boolean> = ref(false)

onBeforeMount(async () => {
  initShoppingCart()
})

const currentStep: ComputedRef<number> = computed(() => store.getters[`${SHOPPING}/getCurrentStep`])
const steps: Ref<any[]> = computed(() => store.getters[`${SHOPPING}/getSteps`])
const rate: Ref<IRate> = computed(() => store.getters[`${SHOPPING}/getRate`])
const channel: Ref<string> = computed(() => store.getters[`${SHOPPING}/${GET_CHANNEL}`])
const username: ComputedRef<string> = computed(() => store.getters[`${SHOPPING}/${GET_USERNAME}`])
const company: ComputedRef<string> = computed(() => store.getters[`${SHOPPING}/${GET_COMPANY}`])
const showNoAccountMessage: ComputedRef<string> = computed(
  () => store.getters[`${SHOPPING}/${GET_SHOW_NO_ACCOUNT_MESSAGE}`]
)
const userInfo: ComputedRef<IUserInfo> = computed(
  () => store.getters[`${SHOPPING}/${GET_USER_INFO}`]
)
async function initShoppingCart() {
  loading.value = true
  store.dispatch(`${SHOPPING}/${CLEAN_SELLING_DATA}`)
  if (configStr?.trademark) {
    changeColors(configStr.trademark)
    store.dispatch(`${SHOPPING}/${SET_TRADEMARK}`, configStr?.trademark)
  }
  await checkUserCredentials(configStr?.username)

  if (configStr?.userInfo) {
    store.dispatch(`${SHOPPING}/${SET_USER_INFO}`, configStr.userInfo)
  } else {
    await store.dispatch(`${SHOPPING}/${GET_USER_INFO}`, username.value)
  }

  if (username.value) {
    configStr?.lang && (await store.dispatch(`${SHOPPING}/${SET_LANG}`, configStr?.lang || 'ca'))
    configStr?.gescal && (await store.dispatch(`${SHOPPING}/${SET_GESCAL}`, configStr?.gescal))

    if (configStr?.cartId) {
      await store.dispatch(`${SHOPPING}/${GET_FREEZE_CART}`, configStr?.cartId)
    } else if (configStr?.opportunityId) {
      await store.dispatch(
        `${SHOPPING}/${GET_FREEZE_CART_WITH_OPPORTUNITY_ID}`,
        configStr?.opportunityId
      )
      store.dispatch(`${SHOPPING}/${SAVE_OPPORTUNITY_ID}`, configStr?.opportunityId)
    } else {
      await store.dispatch(`${SHOPPING}/${SET_RATE_CODE}`, configStr?.rateCode)
      const isProductFamily = ['MobileFiberFix', 'FiberFix', 'Mobile'].includes(
        configStr?.productFamily
      )
      isProductFamily
        ? store.dispatch(`${SHOPPING}/${SET_PRODUCT_FAMILY}`, configStr?.productFamily)
        : store.dispatch(`${SHOPPING}/${SET_PRODUCT_FAMILY}`, 'Mobile')
    }

    const channel: Ref<string> = computed(() => store.getters[`${SHOPPING}/${GET_CHANNEL}`])
    const trademark: Ref<string> = computed(() => store.getters[`${SHOPPING}/${GET_TRADEMARK}`])
    const username: Ref<string> = computed(() => store.getters[`${SHOPPING}/${GET_USERNAME}`])
    changeMainColors(trademark.value ? trademark.value : company.value ? company.value : 'Parlem')

    if (configStr?.userId) {
      await store.dispatch(`${SHOPPING}/${SET_USER_ID}`, configStr.userId)

      if (channel.value !== CHANNEL_WEB) {
        await setOldUserData(configStr.userId, company.value)
      }
    }

    //Inici Rate
    const rateData: IRateDataParams = {
      lang: configStr.lang,
      trademark: configStr.trademark,
      username: username.value,
      company: company.value
    }
    if (configStr.rateCode?.length) {
      rateData.code = configStr.rateCode
      await store.dispatch(`${SHOPPING}/${GET_RATE_BY_CODE}`, rateData)
    } else {
      rateData.family = store.getters[`${SHOPPING}/getProductFamily`]
      /* channel.value === CHANNEL_WEB
        ? await store.dispatch(`${SHOPPING}/${GET_RATE_GROUPS}`, rateData)
        : '' */
    }

    const currentSteps: IStep[] = getInitialSteps()
    const steps: IStep[] = currentSteps.map((step: IStep) => {
      step.description = t(step.description)
      return step
    })
    await store.dispatch(`${SHOPPING}/${SET_STEPS}`, steps)

    const nextStep: IStep | undefined = stepperService.getNextStep()
    if (nextStep?.route) {
      router.push({ path: nextStep.route })
    }
  }
  if (userInfo.value?.status && userInfo.value?.status >= 400) {
    showUserError.value = true
  }
  loading.value = false
}

const userId = computed(() => store.getters[`${SHOPPING}/${GET_USER_ID}`])

watch(
  userId,
  async (newUserId) => {
    if (channel.value !== CHANNEL_WEB) {
      await setOldUserData(newUserId, company.value)
    }
  },
  { immediate: true }
)

function changeMainColors(company: string) {
  return changeColors(company)
}
async function setOldUserData(userId: string, companyName: string): Promise<void> {
  try {
    if (!userId || !companyName) {
      return
    }
    const userData: IInfoUser | any = await apiShoppingcartService.getByUserId(companyName, userId )
    if (userData) {
      store.dispatch(`${SHOPPING}/${SET_OLD_USER}`, userData)
      saveUser(userData)
    }
  } catch (error) {
    error
  }
}

function saveUser(user: IUser): void {
  if (!user) {
    return
  }
  const contact: IContact | undefined = user?.provisionContacts.find(
    (contact: IContact) => contact.isDefault
  )
  const userUpdate: IUserForm = {
    firstName: {
      value: user?.personalData?.firstName,
      disabled: false,
      required: false
    },
    lastName: {
      value: user?.personalData?.lastName,
      disabled: false,
      required: false
    },
    documentNumber: {
      value: user?.personalData?.documentNumber,
      disabled: false,
      required: false
    },
    birthdate: {
      value: moment(user?.personalData?.personBirthdate).format('YYYY-MM-DD'),
      disabled: false,
      required: false
    },
    email: {
      value: contact?.email,
      disabled: false,
      required: false
    },
    phone: {
      value: contact?.phone,
      disabled: false,
      required: false
    },
    nationality: {
      value: user?.personalData?.nationality,
      disabled: false,
      required: false
    },
    customerType: {
      value: user?.personalData?.customerType,
      disabled: false,
      required: false
    }
  }
  store.dispatch(`${SHOPPING}/${UPDATE_NEW_USER}`, userUpdate)
}

function getInitialSteps(): IStep[] {
  return channel.value === CHANNEL_WEB ? WEB_CHANNEL_STEPS : INITIAL_STEPS
}

function goTostep(step: any): void {
  const selectedStep: number = Number(step)
  const stepCompleted: number = store.getters[`${SHOPPING}/getStepCompleted`]

  if (stepCompleted < selectedStep - 1) {
    return
  }

  const nextStep: IStep | undefined = stepperService.getNextStep(selectedStep)

  if (nextStep?.route) {
    router.push({ path: nextStep.route })
  }

  store.dispatch(`${SHOPPING}/${SET_STEP}`, selectedStep)
}
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
