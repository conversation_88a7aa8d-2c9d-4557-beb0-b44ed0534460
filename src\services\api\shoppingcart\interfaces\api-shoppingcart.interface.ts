import type { ILeadSell } from './lead-sell.interface'
import type { IApiRes } from '../../interfaces'
import type { IRateDataParams } from './rate-data-params.interface'
import type { IRate } from './rate.interface'
import type { IfreezeCart } from '@/interfaces/freezeCart.interface'
import type { IUserInfo } from '@/services/users/interfaces/user-info.interface'
import type { IInfoUser } from './user-info.interface'

export interface IApiShoppingcart {
  postLeadSell(lead: ILeadSell): Promise<IApiRes<ILeadSell>>
  postFreezeCart(cart: IfreezeCart): Promise<IApiRes<IfreezeCart>>
  putFreezeCart(cart: IfreezeCart): Promise<IApiRes<IfreezeCart>>
  getFreezeCart(cartId: string): Promise<IApiRes<IfreezeCart>>
  getFreezeCartWithOpportunityId(opportunityId: string): Promise<IApiRes<IfreezeCart>>
  deleteFreezeCart(cartId: string): Promise<IApiRes<number>>
  getLeadSell(sellId: string): Promise<IApiRes<ILeadSell>>
  getRateByCode(rateData: IRateDataParams): Promise<IRate>
  getRateGroups(rateData: IRateDataParams): Promise<IRate[]>
  getRateById(rateData: IRateDataParams): Promise<IRate>
  getUserInfo(username: string): Promise<IUserInfo>
  getByUserId(username: string, company: string): Promise<IInfoUser>
}
