export interface IRate {
  id: string,
  company: string,
  trademark: string,
  rateGroup: string,
  tags: [
    string
  ],
  display: {
    externalName: string,
    pimBusinessKey: string,
    segment: string
  },
  finalPrice: {
    priceWithVATFinal: number,
    priceWithVATOrigin: number,
    priceWithoutVATFinal: number,
    priceWithoutVATOrigin: number
  },
  offerPrice: {
    priceWithVATFinal: number,
    priceWithoutVATFinal: number,
    priceWithoutVATOrigin: number,
    priceWithVATOrigin: number,
    promotionalPrizeEndDate: Date,
    durationDaysPromotionalPrize: number,
    promotionalDurationCalculationMethod: string
  },
  payments: {
    firstWithVATOrigin: number,
    firstWithoutVATOrigin: number
  },
  specifications: {
    permanenceComment: string,
    permanenceTime: string,
    permanence: true
  },
  pack: {
    id: string,
    display: {
      externalName: string,
      pimBusinessKey: string,
      segment: string
    },
    products: [
      {
        id: string,
        display: {
          externalName: string,
          pimBusinessKey: string,
          segment: string
        },
        specifications: {
          mbCall: string,
          mbData: string,
          fibSpeed: string,
          callFixToFixNational: string,
          callFixToMobileNational: string,
          fup: string,
          technologyType: string
        },
        translations: {
          additionalProp1: string,
          additionalProp2: string,
          additionalProp3: string
        }
      }
    ],
    translations: {
      additionalProp1: string,
      additionalProp2: string,
      additionalProp3: string
    }
  },
  charges: [
    {
      id: string,
      display: {
        externalName: string,
        pimBusinessKey: string,
        segment: string
      },
      description: string,
      penaltyPricewithVAT: number,
      penaltyPricewithoutVAT: number,
      subType: string,
      type: string,
      translations: {
        additionalProp1: string,
        additionalProp2: string,
        additionalProp3: string
      }
    }
  ],
  translations: {
    additionalProp1: string,
    additionalProp2: string,
    additionalProp3: string
  }
}