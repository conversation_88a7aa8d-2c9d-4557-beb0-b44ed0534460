import type { IAddress } from './address.interface'
import type { IAdvertisingConfig } from './advertising-config.interface'
import type { IBillingInfo } from './billing-info.interface'
import type { IContact } from './contact.interface'
import type { IGdprRegistry } from './gdpr-registry.interface'
import type { IPersonalData } from './personal-data.interface'
import type { ICompanyStructure } from './company-structure.interface'

export interface IUser {
    id?: string
    advertisingConfigurations: IAdvertisingConfig[]
    gdprRegistries: IGdprRegistry[]
    shippingAddresses: IAddress[]
    billingAddresses: IAddress[]
    personalData: IPersonalData
    billingInfos?: IBillingInfo[]
    provisionContacts: IContact[]
    companyStructure?: ICompanyStructure
}
