import type { <PERSON>ick<PERSON>ist, IPickListValue } from '@/services/api/crm/interfaces'
import type { <PERSON>A<PERSON>ress, IContact, IUser } from '@/services/users/interfaces'
import type { IBillingData } from '@/services/users/interfaces/billing-data.interface'
import type { IState } from '@/store/modules/shopping/interfaces/state.interface'
import type { IAddressForm } from '@/components/billing-data/interfaces/address-form.interface'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import type { IStep } from '@/interfaces/step.interface'
import type { ICatalog } from '@/interfaces/catalog.interface'
import type { IRate } from '@/services/api/shoppingcart/interfaces/rate.interface'
import type { IfreezeCart } from '@/interfaces/freezeCart.interface'
import { PICKLIST_CUSTOM_DISCOUNT } from '@/services/api/constants/api.constants'

export const getMsalConfig = (state: IState): any => {
  return state.msalConfig
}
export const getCurrentStep = (state: IState): number => {
  return state.step
}

export const getCoverageStep = (state: IState): IStep => {
  return state.steps.find((d) => d.route === 'coverage') as IStep
}

export const getStepCompleted = (state: IState): number => {
  return state.stepCompleted
}

export const getSteps = (state: IState): IStep[] => {
  return state.steps
}

export const getCompany = (state: IState): string => {
  return state.company
}

export const getChannel = (state: IState): string => {
  return state.channel
}

export const getTrademark = (state: IState): string => {
  return state.trademark
}

export const getUsername = (state: IState): string => {
  return state.username
}

export const getLang = (state: IState): string => {
  return state.lang
}

export const getRateCode = (state: IState): string | undefined => {
  return state.rateCode
}

export const getRate = (state: IState): IRate | null => {
  return state.rate
}

export const getPickLists = (state: IState): IPickList[] => {
  return state.pickLists
}

export const getDiscounts = (state: IState): IPickListValue[] => {
  const discountPicklist: IPickList | undefined = state.pickLists.find(
    (picklist: IPickList) => picklist?.name === PICKLIST_CUSTOM_DISCOUNT
  )
  const activeDiscounts = discountPicklist
    ? discountPicklist.values.filter((discount: IPickListValue) => discount.active)
    : []
  activeDiscounts.unshift({ label: 'No aplicar cap descompte', value: null, active: true })
  return activeDiscounts
}

export const getOldUser = (state: IState): IUser | null => {
  return state.oldUser
}

export const getSelectedDiscount = (state: IState): IPickListValue | null => {
  return state.selectedDiscount
}

export const getNewUser = (state: IState): IUser | null => {
  return state.newUser
}

export const getOpportunityId = (state: IState): string | null => {
  return state.opportunityId
}

export const getCountries = (state: IState): IPickListValue[] | [] => {
  return state.countries
}

export const getCheckedSimAddress = (state: IState): boolean => {
  return state.checkedSimAddress
}

// export const getCheckedAuthorizedPerson = (state: IState): boolean => {
//   return state.checkedAuthorizedPerson
// }

// export const getAuthorizedPerson = (state: IState): object => {
//   return state.authorizedPerson
// }

export const getStreetTypes = (state: IState): { key: string; value: string }[] | [] => {
  return state.streetTypes.map((streetType: IPickListValue) => {
    const o: { key: string; value: string } = {
      key: streetType.value?.toLowerCase() || '',
      value: streetType.translation || streetType.value?.toLowerCase() || ''
    }
    return o
  })
}

export const getUserDefaultContact = (state: IState): any => {
  return state.newUser?.provisionContacts?.find((contact: IContact) => contact.isDefault)
}

export const getBillingData = (state: IState): IBillingData | null => {
  return state.billingData
}

export const getShowNoAccountMessage = (state: IState): boolean => {
  return state.showNoAccountMessage
}

export const getDefaultBillingAddress = (state: IState): IAddress | undefined => {
  return state.newUser?.billingAddresses.find(
    (billingAddress: IAddress) => billingAddress.isDefault
  )
}

export const getDefaultShippingAddress = (state: IState): IAddress | undefined => {
  return state.newUser?.shippingAddresses.find(
    (shippingAddress: IAddress) => shippingAddress.isDefault
  )
}

export const getSimAddress = (state: IState): IAddressForm => {
  return state.shippingAddress as IAddressForm
}

export const getCoverage = (state: IState): ICoverage | null => {
  return state.coverage
}

export const getCatalog = (state: IState): ICatalog[] | null => {
  return state.catalog
}

export const getProductFamily = (state: IState): string | undefined => {
  return state.productFamily
}

export const getUserType = (state: IState): string | undefined => {
  return state.userType
}

export const getAccountId = (state: IState): string => {
  return state.accountId
}

export const getGescal = (state: IState): string | undefined => {
  return state.gescal
}

export const getFreezeCartId = (state: IState): string | null => {
  return state.freezeCartId
}

export const getFreezeCart = (state: IState): IfreezeCart => {
  return {
    billingData: state.billingData,
    shippingAddress: state.shippingAddress,
    catalog: state.catalog,
    user: state.newUser,
    family: state.productFamily,
    rateCode: state.rateCode,
    company: state.company,
    channel: state.channel,
    trademark: state.trademark,
    username: state.username,
    lang: state.lang,
    step: state.step,
    id: state.freezeCartId,
    opportunityId: state.opportunityId
  }
}

export const getUserId = (state: IState): string | null => {
  return state.accountId
}
