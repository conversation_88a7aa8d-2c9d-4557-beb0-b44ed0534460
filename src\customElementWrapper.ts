import { createApp, defineCustomElement, getCurrentInstance, h } from 'vue'
/* import the fontawesome core */
import { library } from '@fortawesome/fontawesome-svg-core'

/* import font awesome icon component */
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
/* import specific icons */
import {
  faTriangleExclamation,
  faXmarkCircle,
  faXmark,
  faSpinner,
  faPenToSquare,
  faSquarePen,
  faFileInvoice,
  faAngleLeft,
  faPrint
} from '@fortawesome/free-solid-svg-icons'

/* add icons to the library */
library.add(
  faTriangleExclamation,
  faXmark,
  faXmarkCircle,
  faSpinner,
  faPenToSquare,
  faSquarePen,
  faFileInvoice,
  faAngleLeft,
  faPrint
)
export const defineCustomElementWrapped = (component: any, plugins: any[] = []) =>
  defineCustomElement({
    styles: component.styles, // <- this
    render: () => h(component),
    setup() {
      const app = createApp({})
      app.component('font-awesome-icon', FontAwesomeIcon)

      // install plugins
      plugins.forEach(app.use)

      const inst: any = getCurrentInstance()
      if (inst !== null) {
        Object.assign(inst.appContext, app._context)
        Object.assign(inst.provides, app._context.provides)
      }
    }
  })
