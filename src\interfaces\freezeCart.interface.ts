import type { IBillingData } from '@/services/users/interfaces/billing-data.interface'
import type { IUser } from '@/services/users/interfaces'
import type { ICatalog } from '@/interfaces/catalog.interface'
import type { IAddressForm } from '@/components/billing-address/interfaces/address-form.interface'

export interface IfreezeCart {
  id: string | null
  opportunityId: string | null
  billingData?: IBillingData
  catalog?: ICatalog[]
  user?: IUser | null
  family?: string | undefined
  rateCode?: string | undefined
  company?: string | null
  channel?: string
  trademark?: string
  username?: string
  lang?: string
  shippingAddress?: IAddressForm
  step: number
}
