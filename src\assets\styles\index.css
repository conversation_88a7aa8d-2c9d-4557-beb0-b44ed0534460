@import '_fontFace.css';
@import '_fonts.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

.input-error {
  @apply border-error;
}

.disabled {
  @apply pointer-events-none opacity-70;
}
@layer base {
  :root {
    --color-primary: 252 189 41;
    --color-secondary: 0 0 0;
    --color-primary-light: 255 242 210;
  }
}
/* Define an animation behavior */
@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
/* This is the class name given by the Font Awesome component when icon contains 'spinner' */
.fa-spinner {
  /* Apply 'spinner' keyframes looping once every second (1s)  */
  animation: spinner 1s linear infinite;
}

.input-toggle {
  background-image: linear-gradient(45deg, transparent 50%, #9ca3af 50%),
    linear-gradient(135deg, #9ca3af 50%, transparent 50%);
  background-position: calc(100% - 20px) calc(1em + 2px), calc(100% - 15px) calc(1em + 2px),
    calc(100% - 2.5em) 0.5em;
  background-size: 5px 5px, 5px 5px, 1px 1.5em;
  background-repeat: no-repeat;

  &[disabled] {
    background-image: linear-gradient(45deg, transparent 50%, #c8c7cc 50%),
      linear-gradient(135deg, #c8c7cc 50%, transparent 50%);
  }
}

.btn {
  @apply w-full font-bold h-[40px] rounded my-2;
  &.primary {
    @apply bg-primary text-black hover:bg-secondary hover:text-white;
  }
  &.secondary {
    @apply bg-black text-white hover:bg-secondary hover:text-white;
  }
}
