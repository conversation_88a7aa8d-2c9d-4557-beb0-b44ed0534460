import axios from 'axios'

import { LANGUAGES, PICKLISTS } from '../constants/api.constants'
import {
    X_PARLEM_API_KEY,
    CONTENT_TYPE,
    APPLICATION_JSON_PATCH_JSON,
    API,
    CUSTOMERS,
    ADVERTISING,
    SHOPPINGCART
} from '../../constants/services.constants'
import type { IApiRes, IHeaders } from '../interfaces'
import type { IPickList } from './interfaces/picklist.interface'
import type { IApiCrm } from './interfaces/api-crm.interface'
import type { IGdprRegistry } from '@/services/users/interfaces'

const crmHeaders: IHeaders = {
    headers: {
        [X_PARLEM_API_KEY]: import.meta.env.VITE_API_KEY_SHOPPINGCART,
        [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON
    }
}

const apiCrmService: IApiCrm = {
    async getPickLists(lang: string = 'es'): Promise<IPickList[]> {
        const url: string = `${ import.meta.env.VITE_BASE_URL }/${ SHOPPINGCART }/${ API }/${ LANGUAGES }/${ lang }/${ PICKLISTS }`
        const response: IApiRes<IPickList[]> = await axios.get(url, crmHeaders)
        return response && response.data
    },
    async updateAdvertisingSettings(accountId: string, gpdrData: IGdprRegistry): Promise<IGdprRegistry> {
        const url: string = `${ import.meta.env.VITE_BASE_URL }/${ SHOPPINGCART }/${ API }/${ CUSTOMERS }/${ accountId }/${ ADVERTISING }`
        const response: IApiRes<IGdprRegistry> = await axios.patch(url, gpdrData, crmHeaders)
        return response && response.data
    }
}

export default apiCrmService