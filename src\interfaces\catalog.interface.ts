import type { ICoverage } from './coverage-data.interface'

// TODO: move to different files
export interface ICatalog {
  id?: string | number
  detail?: any
  tariffRate?: ITariffRate
  landline?: ILandLine
  mainMobile?: IMobile
  rootProduct?: number | string | null
  installationAddress?: ICoverage
  additionalMobileLines?: IAdditonalMobile[]
  tv?: ITv
  totalPrice?: {
    offerPrice?: number | null
    finalPrice?: number | null
  }
}

export interface ITv {
  data: ICatalogProduct
  id: string
  name: string
  price: number
  products: ICatalogProduct[]
  selected: boolean
  value: string
}

export interface ICatalogProduct {
  name: string
  rateCode: string
  specifications: ISpecifications
  rootProduct?: string | null
}

export interface ISpecifications {
  mbCall: string | null
  mbData: string | null
  fibSpeed: string | null
  callFixToFixNational: string | null
  callFixToMobileNational: string | null
  fup: string | null
  technologyType: string | null
}

export interface ITariffRate {
  charges?: ICharge[]
  data: ICatalogProduct
  durationDaysPromotionalPrize?: number
  finalPrice?: number
  id?: string
  offerPrice?: number
  permanenceComment?: string
  products?: ICatalogProduct[]
  rateGroupId?: string
  mobileData?: IMobileData
  isTv?: boolean
}

export interface ICharge {
  description: string
  penaltyPrice: number
}

export interface IMobile {
  data: IMobileData
  meteredGbytes: number
}

export interface IMobileData {
  company: string
  icc: string
  paymentType: string
  phone: string
  portability: boolean | null
  iccParlem?: string | null
}

export interface IAdditonalMobile {
  id: string
  data: ICatalogProduct
  meteredGbytes: number
  mobileData: IMobileData
  price: number
  products: ICatalogProduct[]
}

export interface ILandLine {
  broadbandSpeed: number
  data: IMobileData
}
