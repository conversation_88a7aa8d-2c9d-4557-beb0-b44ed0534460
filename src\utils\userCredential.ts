import store from '@/store/index'
import { checkUserCredentialsMsal } from 'parlem-webcomponents-common'
import type { IAccount } from '@/store/modules/shopping/interfaces/account.interface'
import {
  SHOPPING,
  SET_ACCESS_TOKEN,
  GET_MSAL_CONFIG,
  SET_SHOW_NO_ACCOUNT_MESSAGE
} from '@/store/modules/shopping/constants/store.constants'
import { SET_USERNAME } from '../store/modules/shopping/constants/store.constants'
// Funció per obtenir el token
export default async function checkUserCredentials(username: string) {
  try {
    const msalConfig = store.getters[`${SHOPPING}/${GET_MSAL_CONFIG}`]
    store.dispatch(`${SHOPPING}/${SET_SHOW_NO_ACCOUNT_MESSAGE}`, false)

    const accessTokenResponse = await checkUserCredentialsMsal(msalConfig)
    if (accessTokenResponse && accessTokenResponse.idToken) {
      const accessToken: string = accessTokenResponse.idToken
      const account: IAccount = accessTokenResponse.account

      store.dispatch(`${SHOPPING}/${SET_ACCESS_TOKEN}`, accessToken)
      store.dispatch(
        `${SHOPPING}/${SET_USERNAME}`,
        username && username.length ? username : account.username ? account.username : 'nouBackEnd'
      )
      if (!account.username && !username) {
        store.dispatch(`${SHOPPING}/${SET_SHOW_NO_ACCOUNT_MESSAGE}`, true)
      }
    } else {
      store.dispatch(
        `${SHOPPING}/${SET_USERNAME}`,
        username && username.length ? username : 'nouBackEnd'
      )
      if (!username) {
        store.dispatch(`${SHOPPING}/${SET_SHOW_NO_ACCOUNT_MESSAGE}`, true)
      }
    }
  } catch (error) {
    store.dispatch(
      `${SHOPPING}/${SET_USERNAME}`,
      username && username.length ? username : 'nouBackEnd'
    )
    if (!username) {
      store.dispatch(`${SHOPPING}/${SET_SHOW_NO_ACCOUNT_MESSAGE}`, true)
    }
  }
}
