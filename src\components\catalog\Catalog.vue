<template>
  <PwSeparator :title="t('stepper.products')"></PwSeparator>

  <div v-if="!loading" class="p-4" ref="pwCatalog"></div>
</template>

<script lang="ts">
import en from '@/i18n/locales/en.json'
import es from '@/i18n/locales/es.json'
import ca from '@/i18n/locales/ca.json'
import gl from '@/i18n/locales/gl.json'

import { useI18n } from 'vue-i18n'
import { ref, onMounted, computed, type Ref, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import type { IStep } from '@/interfaces/step.interface'
import {
  GET_ACCOUNT_ID,
  GET_GESCAL,
  GET_RATE_CODE,
  GET_TRADEMARK,
  GET_USERNAME,
  GET_USER_INFO,
  SET_CATALOG,
  SET_STEP,
  SHOPPING
} from '@/store/modules/shopping/constants/store.constants'
import type { ICatalog } from '@/interfaces/catalog.interface'
import { PwSeparator } from 'parlem-webcomponents-common'
import { leadsService, stepperService } from '@/services'
import type { ILeadCandidate } from '@/interfaces/lead-candidate'
import type { IUser } from '@/services/users/interfaces'
import { CHANNEL_WEB } from '@/services/constants/services.constants'

export default {
  name: 'PwCatalog',
  components: {
    PwSeparator
  },
  setup(props: any, { emit }: any) {
    const pwCatalog: any = ref(null)
    const store = useStore()
    const lang: Ref<string> = computed(() => store.getters[`${SHOPPING}/getLang`])
    const { t, locale }: any = useI18n({
      messages: {
        en,
        gl,
        es,
        ca
      }
    })
    locale.value = lang.value || locale.value
    const steps: Ref<IStep[]> = computed(() => store.getters[`${SHOPPING}/getSteps`])
    let loading: boolean = false
    const maxAdditionalMobiles: number = 4
    const telephoneCompanies = [
      { key: '001', value: 'MOVISTAR' },
      { key: '003', value: 'VODAFONE' },
      { key: '004', value: 'ORANGE' },
      { key: '005', value: 'YOIGO' },
      { key: '006', value: 'EUSKALTEL' },
      { key: '010', value: 'AIRENETWORKS' },
      { key: '023', value: 'FIBRACAT' },
      { key: '044', value: 'MASMOVIL' },
      { key: '045', value: 'LEMONVIL' },
      { key: '053', value: 'TELECABLE MÓVIL' },
      { key: '064', value: 'SIMYO' },
      { key: '507', value: 'ADAMO' },
      { key: '813', value: 'LOWI' },
      { key: '101', value: 'AMENA' },
      { key: '920', value: 'ONO' },
      { key: '940', value: 'JAZZTEL' },
      { key: '813', value: 'FI NETWORK' },
      { key: '70', value: 'BT' },
      { key: '715401', value: 'TUENTI' },
      { key: '725303', value: 'PEPEPHONE' },
      { key: '725503', value: 'HITS' },
      { key: '735014', value: 'THE PHONE HOUSE / HAPPY MOVIL / CABLEMOVIL / REP.M' },
      { key: '735024', value: 'CARREFOUR' },
      { key: '735034', value: 'DIA' },
      { key: '755106', value: 'RACC' },
      { key: '735203', value: '753XX3' },
      { key: '740001', value: 'XPHERA MOVILES' },
      { key: '073', value: 'R CABLE Y TELECOMUNICACIONES' },
      { key: '075', value: 'DIGI SPAIN TELECOM' },
      { key: '76', value: 'VIVA MOBILE' },
      { key: '78', value: 'MOREMINUTES' },
      { key: '79', value: 'VIVAZZI' },
      { key: '084', value: 'LYCAMOBILE' },
      { key: '094', value: 'YOUMOBILE' },
      { key: '054', value: 'LLAMAYA' },
      { key: '104', value: 'TRUPHONE' },
      { key: '154', value: 'AIRE NETWORKS' },
      { key: '213', value: 'LEBARA MOVIL' },
      { key: '290', value: 'PEPEPHONE 3.0' },
      { key: '513', value: 'HITS MOBILE' },
      { key: '813', value: 'VODAFONE ENABLER' },
      { key: '930', value: 'E-PLUS ESPAÑA (SIMYO, BANKINTER, BLAU)' },
      { key: '950', value: 'FONYOU' },
      { key: '960', value: 'PROCONO/PTV TELECOM' },
      { key: '988', value: 'LEAST COST ROUTING TELECOM' },
      { key: '991', value: 'OCEANE' },
      { key: '992', value: 'ORTEL' },
      { key: '993', value: 'TUENTI MOVIL' },
      { key: '904224', value: 'IBERCOM' },
      { key: '970913', value: 'ORBITEL' },
      { key: '970413', value: 'EROSKI MÓVIL' },
      { key: '970400', value: 'NEO SKY' },
      { key: '903124', value: 'IOS-PINGER-ALOW-VOZELIA-KNET-SUOP' },
      { key: '100', value: 'GT MOBILE' },
      { key: '006', value: 'VIRGIN TELCO' },
      { key: '023', value: 'Altecom' },
      { key: '102', value: 'QUATTRE' },
      { key: '103', value: 'TELSOME' },
      { key: '110', value: 'OPERADORS.CAT' },
      { key: '111', value: 'AUREA MOTRIZ' },
      { key: '114', value: 'ONMOVIL' },
      { key: '116', value: 'SARENET' },
      { key: '124', value: 'SUMA' },
      { key: '125', value: 'DRAGONET COMUNICACIONES' },
      { key: '126', value: 'OPENCABLE TELECOMUNICACIONES SL' },
      { key: '135', value: 'THE TELECOM BOUTIQUE' },
      { key: '145', value: 'VOZELIA TELECOM SL' },
      { key: '165', value: 'EVOLUTIO' },
      { key: '174', value: 'OCEANS_GMM' },
      { key: '176', value: 'SUOP' },
      { key: '184', value: 'VOZTELECOM_GMM' },
      { key: '194', value: 'FLEXIMÓVIL' },
      { key: '204', value: 'REPUBLICA MOVIL' },
      { key: '205', value: 'MIMONKEY' },
      { key: '234', value: 'GRUPALIA INTERNET' },
      { key: '244', value: 'PTV TELECOM 4G' },
      { key: '441', value: 'LOBSTER' },
      { key: '502', value: 'ONITI' },
      { key: '504', value: 'ALAI TELECOM' },
      { key: '506', value: 'JOi Mobile' },
      { key: '508', value: 'MOMOFONE' },
      { key: '510', value: 'JETNET' },
      { key: '600', value: 'AOPM' },
      { key: '601', value: 'DIRECCION GENERAL POLICIA' },
      { key: '602', value: 'ADMINISTRACIONES PUBLICAS' },
      { key: '666', value: 'OPERADORFICTICIO' },
      { key: '800', value: 'CMT' },
      { key: '899', value: 'AOP' },
      { key: '900', value: 'ER' },
      { key: '901', value: 'TELEFÓNICA DE ESPAÑA, S.A.U. (FIJO)' },
      { key: '902', value: 'VODAFONE ONO (FIJO)' },
      { key: '903', value: 'FRANCE TELECOM ESPAÑA, S.A. (FIJO)' },
      { key: '904', value: 'DRAGONET COMUNICACIONES, S.L.' },
      { key: '905', value: 'VODAFONE ES (FIJO)' },
      { key: '908', value: 'TELECABLE DE ASTURIAS, S.A.U. (FIJO)' },
      { key: '909', value: 'JET MULTIMEDIA ESPAÑA, S.A.' },
      { key: '910', value: 'MASVOZ TELECOMUNICACIONES INTERACTIVAS, S.L.' },
      { key: '912', value: 'IBERBANDA, S.A.' },
      { key: '914', value: 'R CABLE Y TELECOMUNICACIONES GALICIA, S.A. (FIJO)' },
      { key: '920', value: 'EUSKATEL, S.A. (FIJO)' },
      { key: '922', value: 'SOYTIC TÉCNICA E INNOVACIONES, S.L.' },
      { key: '923', value: 'GRUPALIA INTERNET, S.A.' },
      { key: '924', value: 'BT ESPAÑA CIA DE SERVICIOS GLOBALES DE TLC, S.A.U. (FIJO)' },
      { key: '925', value: 'COLT TELECOM ESPAÑA, S.A.' },
      { key: '926', value: 'VODAFONE ESPAÑA, S.A.U. (FIJO)' },
      { key: '927', value: 'NUMINTEC COMUNICACIONES, S.L.' },
      { key: '928', value: 'NEO-SKY 2002, S.A.' },
      { key: '929', value: 'JAZZ TELECOM, S.A.U. (FIJO)' },
      { key: '931', value: 'XTRA TELECOM, S.L.' },
      { key: '932', value: 'ALAI OPERADOR DE TELECOMUNICACIONES, S.L.' },
      { key: '938', value: 'EQUANT SPAIN, S.A.' },
      { key: '939', value: 'ORANGE CATALUNYA XARXES DE TELECOMUNICACIONES, S.A.' },
      { key: '941', value: '11888 SERVICIO CONSULTA TELEFÓNICA, S.A.U.' },
      { key: '945', value: 'VIDESH SANCHAR NIGAM SPAIN, S.R.L.' },
      { key: '948', value: 'PROCONO, S.A.' },
      { key: '950', value: 'CABLESUR COMUNICACIONES, S.A.' },
      { key: '951', value: 'TELEFÓNICA MÓVILES ESPAÑA, S.A.U. (FIJO)' },
      { key: '953', value: 'PEOPLETEL, S.A.' },
      { key: '956', value: 'VERIZÓN SPAIN, S.L.' },
      { key: '965', value: 'OPERADORA DE TELECOMUNICACIONES OPERA S.L.' },
      { key: '966', value: 'IBERCOM TELECOM, S.A.' },
      { key: '967', value: 'PREMIUM NUMBERS, S.L.' },
      { key: '969', value: 'VOZTELECOM SISTEMAS, S.L.' },
      { key: '972', value: 'TELECOM BUSINESS SOLUTIONS, S.L.' },
      { key: '973', value: 'AIRE NETWORKS DEL MEDITERRÁNEO, S.L.U' },
      { key: '974', value: 'XFERA MÓVILES, S.A. (FIJO)' },
      { key: '975', value: 'KNET COMUNICACIONES, S.L.' },
      { key: '977', value: 'NVÍA GESTIÓN DE DATOS, S.L.' },
      { key: '978', value: 'SINERGYNE GLOBAL COMMUNICATIONS, S.L.' },
      { key: '979', value: 'DELICOM, S.L.U.' },
      { key: '981', value: 'DIALOGA SERVICIOS INTERACTIVOS, S.L.' },
      { key: '983', value: 'ELECTRONIC GROUP TELECOM, S.A.' },
      { key: '986', value: 'EAGERTECH 21, S.L.' },
      { key: '987', value: 'DUOCOM EUROPE, S.L.' },
      { key: '989', value: 'LLEIDA NETWORKS SERVEIS TELEMATICS, S.L.' },
      { key: '990', value: 'CONTACTA SERVICIOS AVANZADOS, S.L.' },
      { key: '991', value: 'INCOTEL SERVICIOS AVANZADOS, S.L.' },
      { key: '992', value: 'INCOTEL INGENIERÍA Y CONSULTORÍA, S.L.' },
      { key: '993', value: 'INTERNET GLOBAL BUSINESS, S.L.' },
      { key: '994', value: 'EMSERTEX 2002, S.L.' },
      { key: '996', value: 'ANDAL MEDIA, S.L.' },
      { key: '997', value: 'APLICACIONS DE SERVEI MONSAN, S.L.' }
    ]
    const showPopup = ref(false)
    const selectedFamily = ref('')
    const channel: Ref<IUser> = computed(() => store.getters[`${SHOPPING}/getChannel`])
    const channelWeb = ref<any>(CHANNEL_WEB)

    onMounted(() => {
      const curr: number = store.getters[`${SHOPPING}/getCurrentStep`]
      const nextStep = steps.value.find((step: IStep) => step.label === curr.toString())
      if (nextStep?.label) {
        store.dispatch(`${SHOPPING}/${SET_STEP}`, parseInt(nextStep.label))
      }

      loadCatalogComponent()
    })
    onBeforeUnmount(() => {
      window.removeEventListener('submit-catalog-event', submitCatalogEvent)
      window.removeEventListener('back-catalog-event', backCatalogEvent)
      window.removeEventListener('upload-service-family-selected', uploadServiceFamilySelected)
    })

    function loadCatalogComponent(): void {
      loading = true
      const trademark: string = store.getters[`${SHOPPING}/${GET_TRADEMARK}`]
      const userInfo: string = store.getters[`${SHOPPING}/${GET_USER_INFO}`]
      const productFamily: string = store.getters[`${SHOPPING}/getProductFamily`]
      const gescal: string = store.getters[`${SHOPPING}/${GET_GESCAL}`]
      const rateCode: string = store.getters[`${SHOPPING}/${GET_RATE_CODE}`]
      const username: string = store.getters[`${SHOPPING}/${GET_USERNAME}`]
      const recoveryCatalogData = store.getters[`${SHOPPING}/getCatalog`]
      const currentStep: number = store.getters[`${SHOPPING}/getCurrentStep`]

      const existScript: HTMLElement | null = document.getElementById('wc-catalog')
      if (!existScript) {
        const catalogScript: HTMLScriptElement = document.createElement('script')
        catalogScript.setAttribute('src', import.meta.env.VITE_CATALOG_WC)
        catalogScript.setAttribute('id', 'wc-catalog')
        catalogScript.async = true
        document.head.appendChild(catalogScript)
      }

      let catalog: HTMLElement = document.createElement('pw-catalog')
      const conf: any = {
        lang: lang.value,
        maxAdditionalMobiles,
        telephoneCompanies,
        username,
        trademark,
        userInfo,
        productFamily,
        gescal,
        rateCode,
        currentStep
      }
      const data: any = JSON.stringify(recoveryCatalogData)
      const configStr = JSON.stringify(conf)
      catalog.setAttribute('config', configStr)
      if (recoveryCatalogData) {
        catalog.setAttribute('data', data)
      }
      if (pwCatalog.value) {
        pwCatalog.value.appendChild(catalog)
      }
      loading = false
    }

    window.addEventListener('submit-catalog-event', submitCatalogEvent)
    function submitCatalogEvent(e: any) {
      const catalog: ICatalog[] = e.detail as ICatalog[]
      store.dispatch(`${SHOPPING}/${SET_CATALOG}`, catalog)
      const currentStep: number = store.getters[`${SHOPPING}/getCurrentStep`]
      createLeadCandidate(catalog)
      setCompletedStep(currentStep)
      nextStep()
    }

    window.addEventListener('back-catalog-event', backCatalogEvent)
    function backCatalogEvent(e: any) {
      const event: { action: string } = e.detail as { action: string }
      if (event?.action && event.action === 'back') {
        prevStep()
      }
    }

    window.addEventListener('upload-service-family-selected', uploadServiceFamilySelected)
    function uploadServiceFamilySelected(e: any) {
      const family: string = e.detail as string
      const coverage = store.getters[`${SHOPPING}/getCoverage`]
      if (!coverage && family.length && family !== 'Mobile') {
        showPopup.value = true
        selectedFamily.value = family
      }
    }

    function nextStep(): void {
      stepperService.nextStep()
    }

    function prevStep(): void {
      stepperService.prevStep()
    }

    function setCompletedStep(currentStep: number): void {
      stepperService.setCompletedStep(currentStep)
    }

    async function createLeadCandidate(catalog: ICatalog[]): Promise<void> {
      const leadCandidate: ILeadCandidate = getLeadCandidateData(catalog)
      await leadsService.createLeadCandidate(leadCandidate)
    }

    function getLeadCandidateData(catalog: ICatalog[]): ILeadCandidate {
      const newUser: IUser = store.getters[`${SHOPPING}/getNewUser`]
      const userDefaultContact = store.getters[`${SHOPPING}/getUserDefaultContact`]
      const step: number = store.getters[`${SHOPPING}/getCurrentStep`]
      const steps: IStep[] = store.getters[`${SHOPPING}/getSteps`]
      const stepData: IStep | undefined = steps.find((st: IStep) => st.label === step.toString())
      const position: string =
        channel.value !== channelWeb.value
          ? 'Confirmed'
          : stepData
          ? `${stepData.label} - ${stepData.name}`
          : `${step}`
      const accountId: string = store.getters[`${SHOPPING}/${GET_ACCOUNT_ID}`]
      let lead: ILeadCandidate = {
        accountId,
        phone: userDefaultContact?.phone,
        email: userDefaultContact?.email,
        firstName: newUser?.personalData?.firstName,
        lastName1: newUser?.personalData?.lastName,
        fiscalIdNumber: newUser?.personalData?.documentNumber,
        birthdate: newUser?.personalData?.personBirthdate,
        position,
        productId: catalog[0]?.tariffRate?.data.rateCode || null,
        description: catalog[0]?.tariffRate?.data?.name || null
      }
      /*     catalog
        ? catalog.forEach((catalogDetail: ICatalog) =>
            (catalogDetail?.tariffRate?.data.rateCode
              ? lead.productId?.push(catalogDetail.tariffRate.data.rateCode)
              : null) && catalogDetail?.tariffRate?.data?.name
              ? lead.description?.push(catalogDetail.tariffRate.data.name)
              : null
          )
        : null */
      return lead
    }

    return {
      t,
      pwCatalog,
      showPopup,
      loading
    }
  }
}
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
